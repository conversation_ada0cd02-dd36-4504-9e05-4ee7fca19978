"""
NEUROGLYPH Conceptual Tokenizer - Tokenizer semantico atomico.

Implementa tokenizzazione 1:1 simbolo→concetto con zero-splitting garantito.
Ogni simbolo Unicode corrisponde esattamente a un concetto atomico.
"""

from dataclasses import dataclass
from typing import List, Dict, Optional, Set
from enum import Enum
import json
from pathlib import Path


class ConceptType(Enum):
    """Tipi di concetti semantici."""
    QUANTIFIER = "quantifier"
    LOGICAL_CONNECTIVE = "logical_connective"
    INFERENCE = "inference"
    SET_THEORY = "set_theory"
    MATHEMATICS = "mathematics"
    META_CONCEPT = "meta_concept"
    VARIABLE = "variable"
    LITERAL = "literal"
    CONSTANT = "constant"


@dataclass(frozen=True)
class ConceptDefinition:
    """Definizione di un concetto atomico."""
    concept_id: int
    symbol: str
    unicode_codepoint: str
    concept_name: str
    semantic_type: ConceptType
    arity: int
    meaning: str
    logical_strength: float = 1.0
    aliases: List[str] = None
    
    def __post_init__(self):
        if self.aliases is None:
            object.__setattr__(self, 'aliases', [])


@dataclass(frozen=True)
class ConceptToken:
    """Token concettuale atomico."""
    concept_id: int
    symbol: str
    concept_name: str
    semantic_type: ConceptType
    position: int
    length: int
    meaning: str
    logical_strength: float
    
    def __str__(self) -> str:
        return f"ConceptToken({self.symbol}:{self.concept_name})"
    
    def __repr__(self) -> str:
        return f"ConceptToken(id={self.concept_id}, symbol='{self.symbol}', type={self.semantic_type.value})"


class ConceptRegistry:
    """Registry di tutti i concetti NEUROGLYPH."""
    
    def __init__(self, registry_path: Optional[str] = None):
        self.concepts: Dict[str, ConceptDefinition] = {}
        self.concept_by_id: Dict[int, ConceptDefinition] = {}
        self.symbols: Set[str] = set()
        
        if registry_path:
            self.load_from_file(registry_path)
        else:
            self._initialize_core_concepts()
    
    def _initialize_core_concepts(self):
        """Inizializza concetti core NEUROGLYPH."""
        core_concepts = [
            # Quantificatori
            ConceptDefinition(1001, "∀", "U+2200", "universal_quantification", 
                            ConceptType.QUANTIFIER, 3, "for all elements in domain", 1.0),
            ConceptDefinition(1002, "∃", "U+2203", "existential_quantification", 
                            ConceptType.QUANTIFIER, 3, "there exists at least one", 1.0),
            
            # Connettivi logici
            ConceptDefinition(1101, "⇒", "U+21D2", "material_implication", 
                            ConceptType.LOGICAL_CONNECTIVE, 2, "if premise then conclusion", 0.8),
            ConceptDefinition(1102, "⇔", "U+21D4", "biconditional", 
                            ConceptType.LOGICAL_CONNECTIVE, 2, "if and only if", 0.9),
            ConceptDefinition(1103, "∧", "U+2227", "logical_conjunction", 
                            ConceptType.LOGICAL_CONNECTIVE, 2, "logical and", 1.0),
            ConceptDefinition(1104, "∨", "U+2228", "logical_disjunction", 
                            ConceptType.LOGICAL_CONNECTIVE, 2, "logical or", 0.7),
            ConceptDefinition(1105, "¬", "U+00AC", "logical_negation", 
                            ConceptType.LOGICAL_CONNECTIVE, 1, "logical not", 1.0),
            ConceptDefinition(1106, "≡", "U+2261", "logical_equivalence", 
                            ConceptType.LOGICAL_CONNECTIVE, 2, "logically equivalent", 1.0),
            
            # Inferenza
            ConceptDefinition(1201, "⊢", "U+22A2", "syntactic_entailment", 
                            ConceptType.INFERENCE, 2, "syntactically entails", 1.0),
            ConceptDefinition(1202, "⊨", "U+22A8", "semantic_entailment", 
                            ConceptType.INFERENCE, 2, "semantically entails", 1.0),
            ConceptDefinition(1203, "⊭", "U+22AD", "does_not_entail", 
                            ConceptType.INFERENCE, 2, "does not entail", 1.0),
            ConceptDefinition(1204, "⊤", "U+22A4", "logical_truth", 
                            ConceptType.INFERENCE, 0, "logical truth", 1.0),
            ConceptDefinition(1205, "⊥", "U+22A5", "logical_falsehood", 
                            ConceptType.INFERENCE, 0, "logical falsehood", 1.0),
            
            # Teoria degli insiemi
            ConceptDefinition(1301, "∈", "U+2208", "set_membership", 
                            ConceptType.SET_THEORY, 2, "is element of", 1.0),
            ConceptDefinition(1302, "∉", "U+2209", "not_set_membership", 
                            ConceptType.SET_THEORY, 2, "is not element of", 1.0),
            ConceptDefinition(1303, "⊂", "U+2282", "proper_subset", 
                            ConceptType.SET_THEORY, 2, "is proper subset of", 1.0),
            ConceptDefinition(1304, "⊆", "U+2286", "subset_or_equal", 
                            ConceptType.SET_THEORY, 2, "is subset or equal to", 1.0),
            ConceptDefinition(1305, "⊇", "U+2287", "superset_or_equal", 
                            ConceptType.SET_THEORY, 2, "is superset or equal to", 1.0),
            ConceptDefinition(1306, "∩", "U+2229", "set_intersection", 
                            ConceptType.SET_THEORY, 2, "intersection of sets", 1.0),
            ConceptDefinition(1307, "∪", "U+222A", "set_union", 
                            ConceptType.SET_THEORY, 2, "union of sets", 1.0),
            ConceptDefinition(1308, "∅", "U+2205", "empty_set", 
                            ConceptType.SET_THEORY, 0, "empty set", 1.0),
            
            # Matematica
            ConceptDefinition(1401, "∫", "U+222B", "definite_integral", 
                            ConceptType.MATHEMATICS, 3, "definite integral", 1.0),
            ConceptDefinition(1402, "∑", "U+2211", "summation", 
                            ConceptType.MATHEMATICS, 3, "summation over range", 1.0),
            ConceptDefinition(1403, "∏", "U+220F", "product", 
                            ConceptType.MATHEMATICS, 3, "product over range", 1.0),
            ConceptDefinition(1404, "∂", "U+2202", "partial_derivative", 
                            ConceptType.MATHEMATICS, 2, "partial derivative", 1.0),
            ConceptDefinition(1405, "∇", "U+2207", "gradient", 
                            ConceptType.MATHEMATICS, 1, "gradient operator", 1.0),
            ConceptDefinition(1406, "√", "U+221A", "square_root", 
                            ConceptType.MATHEMATICS, 1, "square root", 1.0),
            ConceptDefinition(1407, "∞", "U+221E", "infinity", 
                            ConceptType.MATHEMATICS, 0, "infinity", 1.0),
            
            # Meta-concetti
            ConceptDefinition(1501, "🧠", "U+1F9E0", "reasoning_process", 
                            ConceptType.META_CONCEPT, 1, "reasoning or thinking process", 0.9),
            ConceptDefinition(1502, "⚡", "U+26A1", "function_concept", 
                            ConceptType.META_CONCEPT, 1, "function or energy concept", 0.8),
            ConceptDefinition(1503, "🔄", "U+1F504", "iterative_process", 
                            ConceptType.META_CONCEPT, 1, "iteration or cycle", 0.8),
            ConceptDefinition(1504, "🏛", "U+1F3DB", "structural_concept", 
                            ConceptType.META_CONCEPT, 1, "structural or architectural concept", 0.7),
            ConceptDefinition(1505, "📖", "U+1F4D6", "knowledge_concept", 
                            ConceptType.META_CONCEPT, 1, "knowledge or learning", 0.8),
            ConceptDefinition(1506, "🔮", "U+1F52E", "prediction_concept", 
                            ConceptType.META_CONCEPT, 1, "prediction or foresight", 0.7),
        ]
        
        for concept in core_concepts:
            self.register_concept(concept)
    
    def register_concept(self, concept: ConceptDefinition):
        """Registra un nuovo concetto."""
        self.concepts[concept.symbol] = concept
        self.concept_by_id[concept.concept_id] = concept
        self.symbols.add(concept.symbol)
        
        # Registra anche gli alias
        for alias in concept.aliases:
            self.concepts[alias] = concept
    
    def get_concept(self, symbol: str) -> Optional[ConceptDefinition]:
        """Ottieni definizione concetto da simbolo."""
        return self.concepts.get(symbol)
    
    def get_concept_by_id(self, concept_id: int) -> Optional[ConceptDefinition]:
        """Ottieni definizione concetto da ID."""
        return self.concept_by_id.get(concept_id)
    
    def has_symbol(self, symbol: str) -> bool:
        """Verifica se simbolo è registrato."""
        return symbol in self.concepts
    
    def get_all_symbols(self) -> Set[str]:
        """Ottieni tutti i simboli registrati."""
        return self.symbols.copy()
    
    def load_from_file(self, file_path: str):
        """Carica registry da file JSON."""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        for concept_data in data.get('concepts', []):
            concept = ConceptDefinition(
                concept_id=concept_data['concept_id'],
                symbol=concept_data['symbol'],
                unicode_codepoint=concept_data['unicode_codepoint'],
                concept_name=concept_data['concept_name'],
                semantic_type=ConceptType(concept_data['semantic_type']),
                arity=concept_data['arity'],
                meaning=concept_data['meaning'],
                logical_strength=concept_data.get('logical_strength', 1.0),
                aliases=concept_data.get('aliases', [])
            )
            self.register_concept(concept)
    
    def save_to_file(self, file_path: str):
        """Salva registry su file JSON."""
        concepts_data = []
        for concept in self.concept_by_id.values():
            concepts_data.append({
                'concept_id': concept.concept_id,
                'symbol': concept.symbol,
                'unicode_codepoint': concept.unicode_codepoint,
                'concept_name': concept.concept_name,
                'semantic_type': concept.semantic_type.value,
                'arity': concept.arity,
                'meaning': concept.meaning,
                'logical_strength': concept.logical_strength,
                'aliases': concept.aliases
            })
        
        data = {
            'metadata': {
                'total_concepts': len(concepts_data),
                'version': '2.0',
                'description': 'NEUROGLYPH Conceptual Registry'
            },
            'concepts': concepts_data
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)


class NGConceptualTokenizer:
    """
    Tokenizer concettuale atomico per NEUROGLYPH.
    
    Garantisce mappatura 1:1 simbolo→concetto con zero-splitting.
    """
    
    def __init__(self, concept_registry: ConceptRegistry):
        self.registry = concept_registry
        self.tokenization_stats = {
            'total_tokens': 0,
            'concept_tokens': 0,
            'unknown_symbols': 0,
            'zero_splitting_rate': 0.0
        }
    
    def tokenize(self, neuroglyph_code: str) -> List[ConceptToken]:
        """
        Tokenizza codice NEUROGLYPH in token concettuali atomici.
        
        Args:
            neuroglyph_code: Codice simbolico NEUROGLYPH
            
        Returns:
            Lista di token concettuali atomici
        """
        tokens = []
        position = 0
        
        while position < len(neuroglyph_code):
            # Estrai simbolo atomico
            symbol, length = self._extract_atomic_symbol(neuroglyph_code, position)
            
            if symbol.isspace():
                # Salta whitespace
                position += length
                continue
            
            # Cerca concetto nel registry
            concept = self.registry.get_concept(symbol)
            
            if concept:
                # Crea token concettuale
                token = ConceptToken(
                    concept_id=concept.concept_id,
                    symbol=symbol,
                    concept_name=concept.concept_name,
                    semantic_type=concept.semantic_type,
                    position=position,
                    length=length,
                    meaning=concept.meaning,
                    logical_strength=concept.logical_strength
                )
                tokens.append(token)
                self.tokenization_stats['concept_tokens'] += 1
            else:
                # Simbolo sconosciuto - crea token letterale
                if (self._is_identifier(symbol) or self._is_number(symbol) or
                    symbol.isalnum() or symbol in '(){}[],:;'):
                    # Variabile, numero o letterale
                    token = self._create_literal_token(symbol, position, length)
                    tokens.append(token)
                else:
                    # Simbolo veramente sconosciuto
                    self.tokenization_stats['unknown_symbols'] += 1
                    print(f"⚠️ Unknown symbol: '{symbol}' at position {position}")
            
            self.tokenization_stats['total_tokens'] += 1
            position += length
        
        # Calcola zero-splitting rate
        if self.tokenization_stats['total_tokens'] > 0:
            self.tokenization_stats['zero_splitting_rate'] = (
                self.tokenization_stats['concept_tokens'] / 
                self.tokenization_stats['total_tokens']
            )
        
        return tokens
    
    def _extract_atomic_symbol(self, code: str, position: int) -> tuple[str, int]:
        """
        Estrae simbolo atomico dalla posizione corrente.
        Supporta variabili multi-carattere e simboli Unicode.

        Returns:
            Tuple (simbolo, lunghezza)
        """
        if position >= len(code):
            return "", 0

        char = code[position]

        # 1. Verifica se inizia una variabile/identificatore multi-carattere
        if char.isalpha() or char == '_':
            return self._extract_identifier(code, position)

        # 2. Verifica se è un numero multi-carattere
        if char.isdigit():
            return self._extract_number(code, position)

        # 3. Simboli multi-carattere (emoji, ecc.)
        if ord(char) > 127:  # Non-ASCII
            # Potrebbe essere emoji multi-byte
            if position + 1 < len(code):
                two_char = code[position:position+2]
                if self.registry.has_symbol(two_char):
                    return two_char, 2

            # Simbolo singolo Unicode
            return char, 1

        # 4. Simboli ASCII singoli
        return char, 1

    def _extract_identifier(self, code: str, position: int) -> tuple[str, int]:
        """
        Estrae identificatore multi-carattere: [A-Za-z_][A-Za-z0-9_]*

        Returns:
            Tuple (identificatore, lunghezza)
        """
        start = position
        length = 0

        # Primo carattere: deve essere lettera o underscore
        if position < len(code) and (code[position].isalpha() or code[position] == '_'):
            length += 1
            position += 1

            # Caratteri successivi: lettere, numeri, underscore
            while (position < len(code) and
                   (code[position].isalnum() or code[position] == '_')):
                length += 1
                position += 1

        identifier = code[start:start + length]
        return identifier, length

    def _extract_number(self, code: str, position: int) -> tuple[str, int]:
        """
        Estrae numero multi-carattere: [0-9]+(\.[0-9]+)?([eE][+-]?[0-9]+)?

        Returns:
            Tuple (numero, lunghezza)
        """
        start = position
        length = 0

        # Parte intera
        while position < len(code) and code[position].isdigit():
            length += 1
            position += 1

        # Parte decimale opzionale
        if (position < len(code) and code[position] == '.' and
            position + 1 < len(code) and code[position + 1].isdigit()):
            length += 1  # punto
            position += 1

            while position < len(code) and code[position].isdigit():
                length += 1
                position += 1

        # Notazione scientifica opzionale
        if (position < len(code) and code[position].lower() == 'e'):
            length += 1  # 'e' o 'E'
            position += 1

            # Segno opzionale
            if (position < len(code) and code[position] in '+-'):
                length += 1
                position += 1

            # Esponente
            while position < len(code) and code[position].isdigit():
                length += 1
                position += 1

        number = code[start:start + length]
        return number, length
    
    def _create_literal_token(self, symbol: str, position: int, length: int) -> ConceptToken:
        """Crea token per letterali/variabili/identificatori."""
        # Determina tipo semantico
        if self._is_identifier(symbol):
            concept_type = ConceptType.VARIABLE
            concept_name = f"variable_{symbol}"
            meaning = f"variable or identifier named '{symbol}'"
            logical_strength = 0.6
        elif self._is_number(symbol):
            concept_type = ConceptType.LITERAL
            concept_name = f"number_{symbol}"
            meaning = f"numeric literal {symbol}"
            logical_strength = 1.0  # Numeri hanno significato preciso
        elif symbol in '(){}[]':
            concept_type = ConceptType.LITERAL
            concept_name = f"delimiter_{symbol}"
            meaning = f"structural delimiter '{symbol}'"
            logical_strength = 0.9  # Delimitatori hanno significato strutturale
        elif symbol in ',:;':
            concept_type = ConceptType.LITERAL
            concept_name = f"separator_{symbol}"
            meaning = f"separator '{symbol}'"
            logical_strength = 0.8
        else:
            concept_type = ConceptType.LITERAL
            concept_name = f"literal_{symbol}"
            meaning = f"literal symbol '{symbol}'"
            logical_strength = 0.5

        return ConceptToken(
            concept_id=0,  # ID 0 per letterali
            symbol=symbol,
            concept_name=concept_name,
            semantic_type=concept_type,
            position=position,
            length=length,
            meaning=meaning,
            logical_strength=logical_strength
        )

    def _is_identifier(self, symbol: str) -> bool:
        """Verifica se è un identificatore valido."""
        if not symbol:
            return False

        # Primo carattere: lettera o underscore
        if not (symbol[0].isalpha() or symbol[0] == '_'):
            return False

        # Caratteri successivi: lettere, numeri, underscore
        return all(c.isalnum() or c == '_' for c in symbol[1:])

    def _is_number(self, symbol: str) -> bool:
        """Verifica se è un numero valido."""
        try:
            float(symbol)
            return True
        except ValueError:
            return False
    
    def get_tokenization_stats(self) -> Dict[str, float]:
        """Ottieni statistiche di tokenizzazione."""
        return self.tokenization_stats.copy()
    
    def reset_stats(self):
        """Reset statistiche."""
        self.tokenization_stats = {
            'total_tokens': 0,
            'concept_tokens': 0,
            'unknown_symbols': 0,
            'zero_splitting_rate': 0.0
        }


# Factory functions
def create_conceptual_tokenizer(registry_path: Optional[str] = None) -> NGConceptualTokenizer:
    """Factory per creare tokenizer concettuale."""
    registry = ConceptRegistry(registry_path)
    return NGConceptualTokenizer(registry)


def load_concept_registry(registry_path: str) -> ConceptRegistry:
    """Carica registry concettuale da file."""
    return ConceptRegistry(registry_path)
