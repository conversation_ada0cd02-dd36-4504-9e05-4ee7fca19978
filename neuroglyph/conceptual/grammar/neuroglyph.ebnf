# NEUROGLYPH Formal Grammar (EBNF)
# ===================================
# 
# Grammatica formale per il linguaggio simbolico NEUROGLYPH
# Definisce la sintassi precisa e non ambigua per tutti i costrutti.
#
# PRINCIPI IMMUTABILI:
# 1. Atomicità: 1 simbolo = 1 token = 1 concetto
# 2. Unicità Unicode: nessun duplicato di codepoint
# 3. Reversibilità: AST ↔ NEUROGLYPH senza perdita (≥95%)
# 4. Semantica: mapping preciso a significato matematico/logico
# 5. Scientifico: riproducibilità + certificazione + audit trail

# ============================================================================
# ROOT PRODUCTION
# ============================================================================

neuroglyph_expression ::= logical_expression
                       | quantified_expression  
                       | mathematical_expression
                       | set_expression
                       | function_expression
                       | variable
                       | literal

# ============================================================================
# LOGICAL EXPRESSIONS
# ============================================================================

logical_expression ::= implication
                    | disjunction
                    | conjunction
                    | negation
                    | grouped_logical

implication ::= logical_operand "⇒" logical_operand
             | grouped_logical "⇒" logical_operand
             | logical_operand "⇒" grouped_logical

disjunction ::= logical_operand "∨" logical_operand
             | disjunction "∨" logical_operand

conjunction ::= logical_operand "∧" logical_operand
             | conjunction "∧" logical_operand

negation ::= "¬" logical_operand
          | "¬" grouped_logical

grouped_logical ::= "(" logical_expression ")"

logical_operand ::= variable
                 | function_expression
                 | quantified_expression
                 | grouped_logical

# ============================================================================
# QUANTIFIED EXPRESSIONS
# ============================================================================

quantified_expression ::= universal_quantification
                        | existential_quantification
                        | unique_existential_quantification

universal_quantification ::= "∀" variable domain? ":" logical_expression
                          | "∀" variable domain? " " existential_quantification

existential_quantification ::= "∃" variable domain? ":" logical_expression

unique_existential_quantification ::= "∃!" variable domain? ":" logical_expression

domain ::= " ∈ " set_symbol
        | " ∈ " set_expression

# ============================================================================
# MATHEMATICAL EXPRESSIONS
# ============================================================================

mathematical_expression ::= arithmetic_expression
                          | comparison_expression
                          | calculus_expression

arithmetic_expression ::= addition
                       | subtraction
                       | multiplication
                       | division

addition ::= mathematical_operand "+" mathematical_operand
subtraction ::= mathematical_operand "-" mathematical_operand
multiplication ::= mathematical_operand "*" mathematical_operand
division ::= mathematical_operand "/" mathematical_operand

comparison_expression ::= equality
                       | inequality
                       | less_than
                       | greater_than
                       | less_equal
                       | greater_equal

equality ::= mathematical_operand "=" mathematical_operand
inequality ::= mathematical_operand "≠" mathematical_operand
less_than ::= mathematical_operand "<" mathematical_operand
greater_than ::= mathematical_operand ">" mathematical_operand
less_equal ::= mathematical_operand "≤" mathematical_operand
greater_equal ::= mathematical_operand "≥" mathematical_operand

calculus_expression ::= integral
                      | summation
                      | product
                      | partial_derivative

integral ::= "∫" function_expression "d" variable
          | "∫" variable "d" variable

summation ::= "∑" mathematical_operand
           | "∑" variable "=" mathematical_operand "^" mathematical_operand " " mathematical_operand

product ::= "∏" mathematical_operand
         | "∏" variable "=" mathematical_operand "^" mathematical_operand " " mathematical_operand

partial_derivative ::= "∂" function_expression "/" "∂" variable
                     | "∂" variable "/" "∂" variable

mathematical_operand ::= variable
                      | literal
                      | function_expression
                      | grouped_mathematical

grouped_mathematical ::= "(" mathematical_expression ")"

# ============================================================================
# SET EXPRESSIONS
# ============================================================================

set_expression ::= set_union
                | set_intersection
                | set_membership
                | set_subset
                | set_superset
                | set_proper_subset
                | set_proper_superset

set_union ::= set_operand "∪" set_operand
set_intersection ::= set_operand "∩" set_operand
set_membership ::= variable "∈" set_operand
set_subset ::= set_operand "⊆" set_operand
set_superset ::= set_operand "⊇" set_operand
set_proper_subset ::= set_operand "⊂" set_operand
set_proper_superset ::= set_operand "⊃" set_operand

set_operand ::= set_symbol
             | variable
             | grouped_set

grouped_set ::= "(" set_expression ")"

set_symbol ::= "ℝ" | "ℕ" | "ℤ" | "ℚ" | "ℂ" | "∅"

# ============================================================================
# FUNCTION EXPRESSIONS
# ============================================================================

function_expression ::= function_call
                     | function_composition

function_call ::= function_name "(" argument_list ")"

function_composition ::= function_call "(" function_call ")"

function_name ::= variable

argument_list ::= argument
               | argument "," argument_list

argument ::= variable
          | literal
          | mathematical_expression
          | logical_expression

# ============================================================================
# BASIC ELEMENTS
# ============================================================================

variable ::= single_char_variable
          | multi_char_variable
          | math_symbol_variable

single_char_variable ::= [a-zA-Z]

multi_char_variable ::= [a-zA-Z][a-zA-Z0-9_]*

math_symbol_variable ::= "π" | "e" | "φ" | "θ" | "α" | "β" | "γ" | "δ" 
                      | "λ" | "μ" | "σ" | "ω" | "Ω" | "Δ" | "Γ" | "Λ"

literal ::= number_literal
         | string_literal
         | boolean_literal

number_literal ::= integer
                | decimal
                | scientific

integer ::= [0-9]+
         | "-" [0-9]+

decimal ::= [0-9]+ "." [0-9]+
         | "-" [0-9]+ "." [0-9]+

scientific ::= decimal "e" integer
            | integer "e" integer

string_literal ::= '"' [^"]* '"'
                | "'" [^']* "'"

boolean_literal ::= "True" | "False"

# ============================================================================
# WHITESPACE AND COMMENTS
# ============================================================================

whitespace ::= " " | "\t" | "\n" | "\r"

# Note: Comments are not part of the formal grammar as NEUROGLYPH
# is a pure symbolic language without comment syntax

# ============================================================================
# VALIDATION RULES (Semantic Constraints)
# ============================================================================

# These rules are enforced during semantic analysis, not parsing:
#
# 1. Variable names must not conflict with reserved operators
# 2. Function calls must have appropriate arity for known functions
# 3. Quantified variables must be properly scoped
# 4. Set membership requires compatible types
# 5. Mathematical operations require numeric operands
# 6. Logical operations require boolean operands
# 7. Parentheses must be balanced
# 8. Domain restrictions must be semantically valid
# 9. Calculus expressions must have valid integration/differentiation variables
# 10. All expressions must be semantically complete (no partial constructs)

# ============================================================================
# PRECEDENCE RULES
# ============================================================================

# Operator precedence (highest to lowest):
# 1. Parentheses: ( )
# 2. Function calls: f(x)
# 3. Calculus: ∫, ∑, ∏, ∂
# 4. Negation: ¬
# 5. Multiplication, Division: *, /
# 6. Addition, Subtraction: +, -
# 7. Comparisons: =, ≠, <, >, ≤, ≥
# 8. Set operations: ∈, ⊆, ⊇, ⊂, ⊃
# 9. Set union/intersection: ∪, ∩
# 10. Logical conjunction: ∧
# 11. Logical disjunction: ∨
# 12. Logical implication: ⇒
# 13. Quantifiers: ∀, ∃, ∃!

# ============================================================================
# ASSOCIATIVITY RULES
# ============================================================================

# Left-associative: +, -, *, /, ∧, ∨, ∪, ∩
# Right-associative: ⇒
# Non-associative: =, ≠, <, >, ≤, ≥, ∈, ⊆, ⊇, ⊂, ⊃
# Prefix: ¬, ∀, ∃, ∃!, ∫, ∑, ∏, ∂
