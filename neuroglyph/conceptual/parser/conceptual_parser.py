"""
NEUROGLYPH Conceptual Parser - Parser semantico puro.

Implementa parsing diretto da token concettuali a AST semantico
usando pattern matching concettuale invece di grammatiche sintattiche.
"""

from typing import List, Optional, Dict, Any, Callable
from dataclasses import dataclass
from enum import Enum

from ..tokenizer.conceptual_tokenizer import ConceptToken, ConceptType, NGConceptualTokenizer
from ..ast.conceptual_ast import (
    Concept, ConceptualAST, ConceptMetadata, LogicalStructure, ConceptRelation,
    UniversalQuantification, ExistentialQuantification, MaterialImplication,
    LogicalConjunction, LogicalDisjunction, LogicalNegation, Variable, Literal,
    FunctionCall, BinaryOperation, Integral, Summation, Product, PartialDerivative,
    GroupedExpression,
    create_variable, create_literal, create_implication, create_conjunction,
    create_disjunction, create_negation, create_universal_quantification,
    create_existential_quantification, create_function_call, create_binary_operation,
    create_integral, create_summation, create_product, create_partial_derivative,
    create_grouped_expression
)


class ParseError(Exception):
    """Errore di parsing concettuale."""
    pass


class SemanticParseError(ParseError):
    """Errore di parsing semantico - pattern non riconosciuto."""
    pass


@dataclass
class ConceptPattern:
    """
    Pattern concettuale per riconoscimento semantico.
    
    Definisce una sequenza di tipi di token che corrisponde
    a un concetto logico specifico.
    """
    name: str
    token_pattern: List[str]  # Pattern di simboli/tipi
    semantic_builder: Callable[[List[ConceptToken]], Concept]
    precedence: int = 0
    associativity: str = "left"  # "left", "right", "none"
    
    def matches(self, tokens: List[ConceptToken], start_pos: int = 0) -> bool:
        """Verifica se il pattern matcha a partire dalla posizione."""
        if start_pos + len(self.token_pattern) > len(tokens):
            return False
        
        for i, pattern_element in enumerate(self.token_pattern):
            token = tokens[start_pos + i]
            
            if pattern_element.startswith("TYPE:"):
                # Match per tipo semantico
                expected_type = pattern_element[5:]
                if token.semantic_type.value != expected_type:
                    return False
            elif pattern_element.startswith("SYMBOL:"):
                # Match per simbolo specifico
                expected_symbol = pattern_element[7:]
                if token.symbol != expected_symbol:
                    return False
            elif pattern_element == "VARIABLE":
                # Match per variabile
                if token.semantic_type != ConceptType.VARIABLE:
                    return False
            elif pattern_element == "LITERAL":
                # Match per letterale
                if token.semantic_type != ConceptType.LITERAL:
                    return False
            else:
                # Match diretto per simbolo
                if token.symbol != pattern_element:
                    return False
        
        return True
    
    def build_concept(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce concetto dal pattern matchato."""
        return self.semantic_builder(tokens)


class NGConceptualParser:
    """
    Parser semantico puro per NEUROGLYPH.
    
    Usa pattern matching concettuale per riconoscere strutture logiche
    e costruire AST semantico immutabile.
    """
    
    def __init__(self, tokenizer: NGConceptualTokenizer):
        self.tokenizer = tokenizer
        self.patterns = self._initialize_concept_patterns()
        self.parse_stats = {
            'total_parses': 0,
            'successful_parses': 0,
            'pattern_matches': {},
            'semantic_errors': 0
        }
    
    def _initialize_concept_patterns(self) -> List[ConceptPattern]:
        """Inizializza pattern concettuali per riconoscimento semantico."""
        patterns = []
        
        # Pattern quantificazione universale con funzione: ∀x: P(x)
        patterns.append(ConceptPattern(
            name="universal_quantification_function",
            token_pattern=["∀", "VARIABLE", ":", "VARIABLE", "(", "VARIABLE", ")"],
            semantic_builder=self._build_universal_quantification_function,
            precedence=11
        ))

        # Pattern quantificazione universale semplice: ∀x: P
        patterns.append(ConceptPattern(
            name="universal_quantification",
            token_pattern=["∀", "VARIABLE", ":", "VARIABLE"],
            semantic_builder=self._build_universal_quantification,
            precedence=10
        ))

        # Pattern quantificazione esistenziale con funzione: ∃x: P(x)
        patterns.append(ConceptPattern(
            name="existential_quantification_function",
            token_pattern=["∃", "VARIABLE", ":", "VARIABLE", "(", "VARIABLE", ")"],
            semantic_builder=self._build_existential_quantification_function,
            precedence=11
        ))

        # Pattern quantificazione esistenziale semplice: ∃x: P
        patterns.append(ConceptPattern(
            name="existential_quantification",
            token_pattern=["∃", "VARIABLE", ":", "VARIABLE"],
            semantic_builder=self._build_existential_quantification,
            precedence=10
        ))

        # Pattern quantificazione universale con dominio: ∀x ∈ A: P(x)
        patterns.append(ConceptPattern(
            name="universal_quantification_domain_function",
            token_pattern=["∀", "VARIABLE", "∈", "VARIABLE", ":", "VARIABLE", "(", "VARIABLE", ")"],
            semantic_builder=self._build_universal_quantification_domain_function,
            precedence=12
        ))

        # Pattern quantificazione esistenziale con dominio: ∃x ∈ A: P(x)
        patterns.append(ConceptPattern(
            name="existential_quantification_domain_function",
            token_pattern=["∃", "VARIABLE", "∈", "VARIABLE", ":", "VARIABLE", "(", "VARIABLE", ")"],
            semantic_builder=self._build_existential_quantification_domain_function,
            precedence=12
        ))
        
        # Pattern implicazione: P ⇒ Q
        patterns.append(ConceptPattern(
            name="material_implication",
            token_pattern=["VARIABLE", "⇒", "VARIABLE"],
            semantic_builder=self._build_implication,
            precedence=3
        ))
        
        # Pattern congiunzione: P ∧ Q
        patterns.append(ConceptPattern(
            name="logical_conjunction",
            token_pattern=["VARIABLE", "∧", "VARIABLE"],
            semantic_builder=self._build_conjunction,
            precedence=5
        ))
        
        # Pattern disgiunzione: P ∨ Q
        patterns.append(ConceptPattern(
            name="logical_disjunction",
            token_pattern=["VARIABLE", "∨", "VARIABLE"],
            semantic_builder=self._build_disjunction,
            precedence=4
        ))
        
        # Pattern negazione: ¬P
        patterns.append(ConceptPattern(
            name="logical_negation",
            token_pattern=["¬", "VARIABLE"],
            semantic_builder=self._build_negation,
            precedence=8
        ))

        # OPERAZIONI BINARIE

        # Pattern unione insiemi: A ∪ B
        patterns.append(ConceptPattern(
            name="set_union",
            token_pattern=["VARIABLE", "∪", "VARIABLE"],
            semantic_builder=self._build_set_union,
            precedence=6
        ))

        # Pattern intersezione insiemi: A ∩ B
        patterns.append(ConceptPattern(
            name="set_intersection",
            token_pattern=["VARIABLE", "∩", "VARIABLE"],
            semantic_builder=self._build_set_intersection,
            precedence=6
        ))

        # Pattern appartenenza: x ∈ A
        patterns.append(ConceptPattern(
            name="set_membership",
            token_pattern=["VARIABLE", "∈", "VARIABLE"],
            semantic_builder=self._build_set_membership,
            precedence=7
        ))

        # Pattern sottoinsieme: A ⊆ B
        patterns.append(ConceptPattern(
            name="subset_relation",
            token_pattern=["VARIABLE", "⊆", "VARIABLE"],
            semantic_builder=self._build_subset_relation,
            precedence=7
        ))

        # Pattern addizione: x + y
        patterns.append(ConceptPattern(
            name="addition",
            token_pattern=["VARIABLE", "+", "VARIABLE"],
            semantic_builder=self._build_addition,
            precedence=6
        ))

        # Pattern uguaglianza: x = y
        patterns.append(ConceptPattern(
            name="equality",
            token_pattern=["VARIABLE", "=", "VARIABLE"],
            semantic_builder=self._build_equality,
            precedence=7
        ))

        # Pattern disuguaglianza: x ≠ y
        patterns.append(ConceptPattern(
            name="inequality",
            token_pattern=["VARIABLE", "≠", "VARIABLE"],
            semantic_builder=self._build_inequality,
            precedence=7
        ))

        # Pattern minore uguale: x ≤ y
        patterns.append(ConceptPattern(
            name="less_equal",
            token_pattern=["VARIABLE", "≤", "VARIABLE"],
            semantic_builder=self._build_less_equal,
            precedence=7
        ))

        # Pattern maggiore uguale: x ≥ y
        patterns.append(ConceptPattern(
            name="greater_equal",
            token_pattern=["VARIABLE", "≥", "VARIABLE"],
            semantic_builder=self._build_greater_equal,
            precedence=7
        ))

        # CHIAMATE DI FUNZIONE

        # Pattern chiamata funzione: f(x)
        patterns.append(ConceptPattern(
            name="function_call",
            token_pattern=["VARIABLE", "(", "VARIABLE", ")"],
            semantic_builder=self._build_function_call,
            precedence=9
        ))

        # QUANTIFICATORI ANNIDATI

        # Pattern quantificatori annidati: ∀x ∃y: R(x,y)
        patterns.append(ConceptPattern(
            name="nested_universal_existential",
            token_pattern=["∀", "VARIABLE", "∃", "VARIABLE", ":", "VARIABLE", "(", "VARIABLE", ",", "VARIABLE", ")"],
            semantic_builder=self._build_nested_universal_existential,
            precedence=13
        ))

        # Pattern quantificatori annidati semplice: ∀x ∃y: R
        patterns.append(ConceptPattern(
            name="nested_universal_existential_simple",
            token_pattern=["∀", "VARIABLE", "∃", "VARIABLE", ":", "VARIABLE"],
            semantic_builder=self._build_nested_universal_existential_simple,
            precedence=12
        ))

        # ESPRESSIONI CON PARENTESI

        # Pattern negazione con parentesi: ¬(P ∨ Q)
        patterns.append(ConceptPattern(
            name="negation_parenthesized",
            token_pattern=["¬", "(", "VARIABLE", "∨", "VARIABLE", ")"],
            semantic_builder=self._build_negation_parenthesized,
            precedence=8
        ))

        # Pattern negazione con parentesi congiunzione: ¬(P ∧ Q)
        patterns.append(ConceptPattern(
            name="negation_parenthesized_and",
            token_pattern=["¬", "(", "VARIABLE", "∧", "VARIABLE", ")"],
            semantic_builder=self._build_negation_parenthesized_and,
            precedence=8
        ))

        # IMPLICAZIONI COMPOSTE

        # Pattern implicazione composta: (P ∧ Q) ⇒ R
        patterns.append(ConceptPattern(
            name="compound_implication",
            token_pattern=["(", "VARIABLE", "∧", "VARIABLE", ")", "⇒", "VARIABLE"],
            semantic_builder=self._build_compound_implication,
            precedence=15  # Precedenza molto alta
        ))

        # Pattern implicazione composta senza parentesi: P ∧ Q ⇒ R
        patterns.append(ConceptPattern(
            name="compound_implication_no_parens",
            token_pattern=["VARIABLE", "∧", "VARIABLE", "⇒", "VARIABLE"],
            semantic_builder=self._build_compound_implication_no_parens,
            precedence=14
        ))

        # Pattern implicazione composta con disgiunzione: (P ∨ Q) ⇒ R
        patterns.append(ConceptPattern(
            name="compound_implication_or",
            token_pattern=["(", "VARIABLE", "∨", "VARIABLE", ")", "⇒", "VARIABLE"],
            semantic_builder=self._build_compound_implication_or,
            precedence=4
        ))

        # OPERATORI MATEMATICI AVANZATI

        # Pattern integrale semplice: ∫ f(x) dx
        patterns.append(ConceptPattern(
            name="integral_simple",
            token_pattern=["∫", "VARIABLE", "(", "VARIABLE", ")", "VARIABLE"],
            semantic_builder=self._build_integral_simple,
            precedence=16  # Precedenza molto alta
        ))

        # Pattern integrale con funzione: ∫ f dx
        patterns.append(ConceptPattern(
            name="integral_function",
            token_pattern=["∫", "VARIABLE", "VARIABLE"],
            semantic_builder=self._build_integral_function,
            precedence=15
        ))

        # Pattern integrale alternativo: ∫ f(x) d x (spazi separati)
        patterns.append(ConceptPattern(
            name="integral_spaced",
            token_pattern=["∫", "VARIABLE", "(", "VARIABLE", ")", "d", "VARIABLE"],
            semantic_builder=self._build_integral_spaced,
            precedence=17
        ))

        # Pattern sommatoria semplice: ∑ xᵢ
        patterns.append(ConceptPattern(
            name="summation_simple",
            token_pattern=["∑", "VARIABLE"],
            semantic_builder=self._build_summation_simple,
            precedence=8
        ))

        # Pattern produttoria semplice: ∏ xᵢ
        patterns.append(ConceptPattern(
            name="product_simple",
            token_pattern=["∏", "VARIABLE"],
            semantic_builder=self._build_product_simple,
            precedence=8
        ))

        # Pattern derivata parziale: ∂f/∂x
        patterns.append(ConceptPattern(
            name="partial_derivative",
            token_pattern=["∂", "VARIABLE", "/", "∂", "VARIABLE"],
            semantic_builder=self._build_partial_derivative,
            precedence=9
        ))

        # Pattern derivata parziale con funzione: ∂f(x)/∂x
        patterns.append(ConceptPattern(
            name="partial_derivative_function",
            token_pattern=["∂", "VARIABLE", "(", "VARIABLE", ")", "/", "∂", "VARIABLE"],
            semantic_builder=self._build_partial_derivative_function,
            precedence=9
        ))
        
        # Pattern variabile singola: P
        patterns.append(ConceptPattern(
            name="variable",
            token_pattern=["VARIABLE"],
            semantic_builder=self._build_variable,
            precedence=1
        ))
        
        # Pattern letterale: 42, "hello"
        patterns.append(ConceptPattern(
            name="literal",
            token_pattern=["LITERAL"],
            semantic_builder=self._build_literal,
            precedence=1
        ))

        # Pattern simboli speciali singoli
        patterns.append(ConceptPattern(
            name="empty_set",
            token_pattern=["∅"],
            semantic_builder=self._build_empty_set,
            precedence=2
        ))

        # Ordina per precedenza (più alta prima)
        patterns.sort(key=lambda p: p.precedence, reverse=True)

        return patterns
    
    def parse(self, neuroglyph_code: str) -> ConceptualAST:
        """
        Parse codice NEUROGLYPH in AST concettuale.
        
        Args:
            neuroglyph_code: Codice simbolico NEUROGLYPH
            
        Returns:
            AST concettuale immutabile
        """
        self.parse_stats['total_parses'] += 1
        
        try:
            # 1. Tokenizza in concetti atomici
            tokens = self.tokenizer.tokenize(neuroglyph_code)
            
            # 2. Filtra whitespace e token non significativi
            filtered_tokens = self._filter_tokens(tokens)
            
            # 3. Parse ricorsivo con pattern matching
            root_concept = self._parse_expression(filtered_tokens)
            
            # 4. Costruisci AST completo
            ast = self._build_conceptual_ast(root_concept, filtered_tokens)
            
            self.parse_stats['successful_parses'] += 1
            return ast
            
        except Exception as e:
            self.parse_stats['semantic_errors'] += 1
            raise SemanticParseError(f"Failed to parse '{neuroglyph_code}': {e}")
    
    def _filter_tokens(self, tokens: List[ConceptToken]) -> List[ConceptToken]:
        """Filtra token non significativi per parsing."""
        filtered = []
        for token in tokens:
            # Salta solo whitespace puro
            if token.symbol.isspace():
                continue
            # Mantieni tutti gli altri token (variabili, letterali, simboli)
            filtered.append(token)
        return filtered
    
    def _parse_expression(self, tokens: List[ConceptToken]) -> Concept:
        """
        Parse ricorsivo di espressione usando pattern matching.
        
        Usa precedenza degli operatori per parsing corretto.
        """
        if not tokens:
            raise SemanticParseError("Empty token sequence")
        
        # Cerca pattern con precedenza più alta
        for pattern in self.patterns:
            for start_pos in range(len(tokens)):
                if pattern.matches(tokens, start_pos):
                    # Pattern trovato - estrai token matchati
                    matched_tokens = tokens[start_pos:start_pos + len(pattern.token_pattern)]
                    
                    # Aggiorna statistiche
                    pattern_name = pattern.name
                    self.parse_stats['pattern_matches'][pattern_name] = (
                        self.parse_stats['pattern_matches'].get(pattern_name, 0) + 1
                    )
                    
                    # Costruisci concetto
                    concept = pattern.build_concept(matched_tokens)

                    # VALIDAZIONE SEMANTICA POST-PARSING
                    self._validate_semantic_correctness(concept, matched_tokens, pattern.name)

                    return concept
        
        # 🚨 RIGORE ASSOLUTO: NESSUN FALLBACK PERMISSIVO
        # Se nessun pattern matcha, l'input è sintatticamente invalido
        # PRINCIPIO IMMUTABILE: Solo pattern semanticamente validi sono accettati
        
        raise SemanticParseError(f"No semantic pattern matches token sequence: {[t.symbol for t in tokens]}")
    
    def _build_conceptual_ast(self, root_concept: Concept, tokens: List[ConceptToken]) -> ConceptualAST:
        """Costruisce AST concettuale completo."""
        # Calcola metadati
        metadata = ConceptMetadata(
            complexity_score=self._calculate_complexity(root_concept),
            semantic_depth=self._calculate_depth(root_concept),
            concept_count=len(tokens),
            logical_operators=self._extract_logical_operators(tokens)
        )
        
        # Costruisci struttura logica
        logical_structure = LogicalStructure(
            depth=self._calculate_depth(root_concept),
            operator_precedence=self._extract_operator_precedence(tokens),
            quantifier_scope={}
        )
        
        # Relazioni concettuali (per ora vuote)
        semantic_relations = []
        
        return ConceptualAST(
            root_concept=root_concept,
            semantic_relations=semantic_relations,
            logical_structure=logical_structure,
            concept_metadata=metadata
        )
    
    # Pattern builders
    def _build_universal_quantification(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificazione universale da token."""
        # Pattern: ∀x: P(x)
        variable = tokens[1].symbol
        body_var = tokens[3].symbol
        body = create_variable(body_var)
        return create_universal_quantification(variable, None, body)
    
    def _build_existential_quantification(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificazione esistenziale da token."""
        # Pattern: ∃x: P
        variable = tokens[1].symbol
        body_var = tokens[3].symbol
        body = create_variable(body_var)
        return create_existential_quantification(variable, None, body)

    def _build_universal_quantification_function(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificazione universale con funzione da token."""
        # Pattern: ∀x: P(x)
        variable = tokens[1].symbol
        function_name = tokens[3].symbol
        arg_var = tokens[5].symbol
        body = create_function_call(function_name, [create_variable(arg_var)])
        return create_universal_quantification(variable, None, body)

    def _build_existential_quantification_function(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificazione esistenziale con funzione da token."""
        # Pattern: ∃x: P(x)
        variable = tokens[1].symbol
        function_name = tokens[3].symbol
        arg_var = tokens[5].symbol
        body = create_function_call(function_name, [create_variable(arg_var)])
        return create_existential_quantification(variable, None, body)

    def _build_universal_quantification_domain_function(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificazione universale con dominio e funzione da token."""
        # Pattern: ∀x ∈ A: P(x)
        variable = tokens[1].symbol
        domain = create_variable(tokens[3].symbol)
        function_name = tokens[5].symbol
        arg_var = tokens[7].symbol
        body = create_function_call(function_name, [create_variable(arg_var)])
        return create_universal_quantification(variable, domain, body)

    def _build_existential_quantification_domain_function(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificazione esistenziale con dominio e funzione da token."""
        # Pattern: ∃x ∈ A: P(x)
        variable = tokens[1].symbol
        domain = create_variable(tokens[3].symbol)
        function_name = tokens[5].symbol
        arg_var = tokens[7].symbol
        body = create_function_call(function_name, [create_variable(arg_var)])
        return create_existential_quantification(variable, domain, body)
    
    def _build_implication(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce implicazione da token."""
        # Pattern: P ⇒ Q
        premise = create_variable(tokens[0].symbol)
        conclusion = create_variable(tokens[2].symbol)
        return create_implication(premise, conclusion)
    
    def _build_conjunction(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce congiunzione da token."""
        # Pattern: P ∧ Q
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        return create_conjunction(left, right)
    
    def _build_disjunction(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce disgiunzione da token."""
        # Pattern: P ∨ Q
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        return create_disjunction(left, right)
    
    def _build_negation(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce negazione da token."""
        # Pattern: ¬P
        operand = create_variable(tokens[1].symbol)
        return create_negation(operand)
    
    def _build_variable(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce variabile da token."""
        return create_variable(tokens[0].symbol)
    
    def _build_literal(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce letterale da token."""
        return self._token_to_literal(tokens[0])

    def _build_empty_set(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce insieme vuoto da token."""
        return create_literal("∅", "special")

    # Builder per operazioni binarie
    def _build_set_union(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce unione di insiemi da token."""
        # Pattern: A ∪ B
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        return create_binary_operation(left, "∪", right)

    def _build_set_intersection(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce intersezione di insiemi da token."""
        # Pattern: A ∩ B
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        return create_binary_operation(left, "∩", right)

    def _build_set_membership(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce appartenenza a insieme da token."""
        # Pattern: x ∈ A
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        return create_binary_operation(left, "∈", right)

    def _build_subset_relation(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce relazione di sottoinsieme da token."""
        # Pattern: A ⊆ B
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        return create_binary_operation(left, "⊆", right)

    def _build_addition(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce addizione da token."""
        # Pattern: x + y
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        return create_binary_operation(left, "+", right)

    def _build_equality(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce uguaglianza da token."""
        # Pattern: x = y
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        return create_binary_operation(left, "=", right)

    def _build_inequality(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce disuguaglianza da token."""
        # Pattern: x ≠ y
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        return create_binary_operation(left, "≠", right)

    def _build_less_equal(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce minore uguale da token."""
        # Pattern: x ≤ y
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        return create_binary_operation(left, "≤", right)

    def _build_greater_equal(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce maggiore uguale da token."""
        # Pattern: x ≥ y
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        return create_binary_operation(left, "≥", right)

    def _build_function_call(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce chiamata di funzione da token."""
        # Pattern: f(x)
        function_name = tokens[0].symbol
        arg = create_variable(tokens[2].symbol)
        return create_function_call(function_name, [arg])

    # Builder per quantificatori annidati
    def _build_nested_universal_existential(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificatori annidati con funzione da token."""
        # Pattern: ∀x ∃y: R(x,y)
        outer_var = tokens[1].symbol
        inner_var = tokens[3].symbol
        function_name = tokens[5].symbol
        arg1 = create_variable(tokens[7].symbol)
        arg2 = create_variable(tokens[9].symbol)

        # Costruisci body con chiamata di funzione
        body = create_function_call(function_name, [arg1, arg2])

        # Costruisci quantificazione interna
        inner_quant = create_existential_quantification(inner_var, None, body)

        # Costruisci quantificazione esterna
        return create_universal_quantification(outer_var, None, inner_quant)

    def _build_nested_universal_existential_simple(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificatori annidati semplici da token."""
        # Pattern: ∀x ∃y: R
        outer_var = tokens[1].symbol
        inner_var = tokens[3].symbol
        body_var = tokens[5].symbol

        # Costruisci body semplice
        body = create_variable(body_var)

        # Costruisci quantificazione interna
        inner_quant = create_existential_quantification(inner_var, None, body)

        # Costruisci quantificazione esterna
        return create_universal_quantification(outer_var, None, inner_quant)

    # Builder per negazione con parentesi
    def _build_negation_parenthesized(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce negazione con parentesi da token."""
        # Pattern: ¬(P ∨ Q)
        left = create_variable(tokens[2].symbol)
        right = create_variable(tokens[4].symbol)

        # Costruisci disgiunzione
        disjunction = create_disjunction(left, right)

        # Costruisci espressione raggruppata per mantenere parentesi
        grouped = create_grouped_expression(disjunction)

        # Costruisci negazione
        return create_negation(grouped)

    def _build_negation_parenthesized_and(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce negazione con parentesi e congiunzione da token."""
        # Pattern: ¬(P ∧ Q)
        left = create_variable(tokens[2].symbol)
        right = create_variable(tokens[4].symbol)

        # Costruisci congiunzione
        conjunction = create_conjunction(left, right)

        # Costruisci espressione raggruppata per mantenere parentesi
        grouped = create_grouped_expression(conjunction)

        # Costruisci negazione
        return create_negation(grouped)

    # Builder per implicazioni composte
    def _build_compound_implication(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce implicazione composta da token."""
        # Pattern: (P ∧ Q) ⇒ R
        left = create_variable(tokens[1].symbol)
        right = create_variable(tokens[3].symbol)
        conclusion = create_variable(tokens[6].symbol)

        # Costruisci premessa (P ∧ Q)
        premise = create_conjunction(left, right)

        # Costruisci espressione raggruppata per mantenere parentesi
        grouped_premise = create_grouped_expression(premise)

        # Costruisci implicazione
        return create_implication(grouped_premise, conclusion)

    def _build_compound_implication_no_parens(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce implicazione composta senza parentesi da token."""
        # Pattern: P ∧ Q ⇒ R
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        conclusion = create_variable(tokens[4].symbol)

        # Costruisci premessa (P ∧ Q)
        premise = create_conjunction(left, right)

        # Costruisci implicazione
        return create_implication(premise, conclusion)

    # Builder per operatori matematici avanzati
    def _build_integral_simple(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce integrale semplice da token."""
        # Pattern: ∫ f(x) dx
        function_name = tokens[1].symbol
        arg_var = tokens[3].symbol
        dx_token = tokens[5].symbol  # Dovrebbe essere "dx"

        # Estrai variabile di integrazione da "dx"
        if dx_token.startswith('d') and len(dx_token) > 1:
            integration_var = dx_token[1:]  # Rimuovi 'd' da 'dx'
        else:
            integration_var = arg_var  # Fallback

        # Costruisci funzione
        function = create_function_call(function_name, [create_variable(arg_var)])

        # Costruisci integrale
        return create_integral(function, integration_var)

    def _build_integral_function(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce integrale con funzione da token."""
        # Pattern: ∫ f dx
        function_name = tokens[1].symbol
        dx_token = tokens[2].symbol  # Dovrebbe essere "dx"

        # Estrai variabile di integrazione da "dx"
        if dx_token.startswith('d') and len(dx_token) > 1:
            integration_var = dx_token[1:]  # Rimuovi 'd' da 'dx'
        else:
            integration_var = 'x'  # Fallback

        # Costruisci funzione
        function = create_variable(function_name)

        # Costruisci integrale
        return create_integral(function, integration_var)

    def _build_summation_simple(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce sommatoria semplice da token."""
        # Pattern: ∑ xᵢ
        expression = create_variable(tokens[1].symbol)
        return create_summation(expression)

    def _build_product_simple(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce produttoria semplice da token."""
        # Pattern: ∏ xᵢ
        expression = create_variable(tokens[1].symbol)
        return create_product(expression)

    def _build_partial_derivative(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce derivata parziale da token."""
        # Pattern: ∂f/∂x
        function_name = tokens[1].symbol
        variable = tokens[4].symbol

        # Costruisci funzione
        function = create_variable(function_name)

        # Costruisci derivata
        return create_partial_derivative(function, variable)

    def _build_partial_derivative_function(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce derivata parziale con funzione da token."""
        # Pattern: ∂f(x)/∂x
        function_name = tokens[1].symbol
        arg_var = tokens[3].symbol
        variable = tokens[7].symbol

        # Costruisci funzione
        function = create_function_call(function_name, [create_variable(arg_var)])

        # Costruisci derivata
        return create_partial_derivative(function, variable)

    def _build_compound_implication_or(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce implicazione composta con disgiunzione da token."""
        # Pattern: (P ∨ Q) ⇒ R
        left = create_variable(tokens[1].symbol)
        right = create_variable(tokens[3].symbol)
        conclusion = create_variable(tokens[6].symbol)

        # Costruisci premessa (P ∨ Q)
        premise = create_disjunction(left, right)

        # Costruisci implicazione
        return create_implication(premise, conclusion)

    def _build_integral_spaced(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce integrale con spazi da token."""
        # Pattern: ∫ f(x) d x
        function_name = tokens[1].symbol
        arg_var = tokens[3].symbol
        integration_var = tokens[6].symbol

        # Costruisci funzione
        function = create_function_call(function_name, [create_variable(arg_var)])

        # Costruisci integrale
        return create_integral(function, integration_var)

    def _token_to_variable(self, token: ConceptToken) -> Variable:
        """
        Converte token in variabile con validazione semantica rigorosa.

        PRINCIPI IMMUTABILI:
        - Solo nomi validi di variabili
        - Nessun operatore logico/matematico
        - Nessun simbolo riservato
        """
        if not token.symbol:
            raise SemanticParseError("Empty variable name not allowed")

        symbol = token.symbol.strip()

        # Blocca nomi vuoti o solo spazi
        if not symbol:
            raise SemanticParseError("Whitespace-only variable name not allowed")

        # Blocca operatori logici come nomi di variabili
        logical_operators = {
            '∀', '∃', '∧', '∨', '⇒', '¬', '⊢', '⊨', '≡', '⇔',
            '∪', '∩', '∈', '⊆', '⊇', '⊂', '⊃', '∅',
            '∫', '∑', '∏', '∂', '∇', '△', '∞',
            '+', '-', '*', '/', '=', '≠', '<', '>', '≤', '≥',
            '(', ')', '[', ']', '{', '}', ':', ';', ',', '.',
            'd', 'dx', 'dy', 'dz'  # Differenziali
        }

        if symbol in logical_operators:
            raise SemanticParseError(f"Reserved operator '{symbol}' cannot be used as variable name")

        # Blocca pattern sintatticamente invalidi
        invalid_patterns = {
            'BROKEN_SYNTAX', '¬∨', '∀∃', '⇒∧', '∩∪', '∈⊆',
            '', '   ', '∧', '∨', '⇒', '¬', '∀', '∃'
        }

        if symbol in invalid_patterns:
            raise SemanticParseError(f"Invalid pattern '{symbol}' cannot be used as variable name")

        # Validazione caratteri: deve essere identificatore valido o simbolo matematico singolo
        if len(symbol) == 1:
            # Simboli matematici singoli sono OK (x, y, z, A, B, etc.)
            if symbol.isalpha() or symbol in {'ℝ', 'ℕ', 'ℤ', 'ℚ', 'ℂ', 'π', 'e', 'φ', 'θ', 'α', 'β', 'γ'}:
                return create_variable(symbol)
            else:
                raise SemanticParseError(f"Invalid single character variable '{symbol}'")
        else:
            # Nomi multi-carattere devono essere identificatori Python validi
            if symbol.isidentifier():
                return create_variable(symbol)
            else:
                raise SemanticParseError(f"Invalid multi-character variable name '{symbol}'")

    def _token_to_literal(self, token: ConceptToken) -> Literal:
        """Converte token in letterale."""
        if "number" in token.concept_name:
            try:
                if '.' in token.symbol:
                    value = float(token.symbol)
                    literal_type = "float"
                else:
                    value = int(token.symbol)
                    literal_type = "int"
            except ValueError:
                value = token.symbol
                literal_type = "string"
        else:
            value = token.symbol
            literal_type = "string"
        
        return create_literal(value, literal_type)

    def _validate_semantic_correctness(self, concept: Concept, tokens: List[ConceptToken], pattern_name: str):
        """
        Validazione semantica post-parsing rigorosa.

        PRINCIPI IMMUTABILI:
        - Nessun pattern sintatticamente invalido
        - Nessun operatore isolato
        - Nessuna struttura logica malformata
        """
        # Validazione 1: Blocca pattern sintatticamente invalidi
        token_symbols = [t.symbol for t in tokens]

        # Pattern che DEVONO essere rifiutati
        invalid_sequences = [
            ['BROKEN_SYNTAX'],
            ['¬', '∨'],  # ¬∨P
            ['∀', '∃'],  # ∀∃x
            ['∧', '∨'],  # ∧∨
            ['⇒', '∧'],  # ⇒∧
            ['∩', '∪'],  # ∩∪
            ['∈', '⊆'],  # ∈⊆
            ['=', '≠'],  # =≠
            ['∫'],       # ∫ da solo
            ['∑'],       # ∑ da solo
            ['∏'],       # ∏ da solo
            ['∂'],       # ∂ da solo
            ['d'],       # d da solo
            ['dx'],      # dx da solo
        ]

        for invalid_seq in invalid_sequences:
            if token_symbols == invalid_seq:
                raise SemanticParseError(f"Invalid syntactic pattern: {' '.join(invalid_seq)}")

        # Validazione 2: Blocca operatori isolati come variabili
        if pattern_name in ['single_variable', 'single_literal'] and len(tokens) == 1:
            symbol = tokens[0].symbol
            operators = {'∀', '∃', '∧', '∨', '⇒', '¬', '∪', '∩', '∈', '⊆', '∫', '∑', '∏', '∂'}
            if symbol in operators:
                raise SemanticParseError(f"Operator '{symbol}' cannot be used as standalone variable")

        # Validazione 3: Verifica struttura logica
        if hasattr(concept, 'to_neuroglyph'):
            try:
                # Test di serializzazione per verificare integrità
                serialized = concept.to_neuroglyph()
                if not serialized or serialized.isspace():
                    raise SemanticParseError(f"Concept serializes to empty/whitespace: '{serialized}'")
            except Exception as e:
                raise SemanticParseError(f"Concept serialization failed: {e}")

        # Validazione 4: Verifica coerenza semantica
        if pattern_name.startswith('quantifier') or 'quantification' in pattern_name:
            # Quantificatori devono avere variabile e body validi
            if hasattr(concept, 'variable') and hasattr(concept, 'body'):
                if not concept.variable or not concept.body:
                    raise SemanticParseError(f"Quantifier missing variable or body")

        # Validazione 5: Blocca pattern che generano output invalidi
        known_invalid_outputs = {
            'BROKEN_SYNTAX', '¬∨', '∀∃', '⇒∧', '∩∪', '∈⊆',
            '', '   ', '∧', '∨', '⇒', '¬', '∀', '∃'
        }

        if hasattr(concept, 'to_neuroglyph'):
            try:
                output = concept.to_neuroglyph()
                if output in known_invalid_outputs:
                    raise SemanticParseError(f"Concept generates invalid output: '{output}'")
            except:
                pass  # Errore di serializzazione già gestito sopra

    # Utility methods
    def _calculate_complexity(self, concept: Concept) -> float:
        """Calcola complessità concettuale."""
        return concept.metadata.complexity_score
    
    def _calculate_depth(self, concept: Concept) -> int:
        """Calcola profondità semantica."""
        return concept.metadata.semantic_depth
    
    def _extract_logical_operators(self, tokens: List[ConceptToken]) -> List[str]:
        """Estrae operatori logici dai token."""
        operators = []
        for token in tokens:
            if token.concept_name in ['universal_quantification', 'existential_quantification',
                                    'material_implication', 'logical_conjunction', 
                                    'logical_disjunction', 'logical_negation']:
                operators.append(token.symbol)
        return operators
    
    def _extract_operator_precedence(self, tokens: List[ConceptToken]) -> List[str]:
        """Estrae precedenza operatori."""
        # Semplificato per ora
        return [t.symbol for t in tokens if t.semantic_type != ConceptType.VARIABLE]
    
    def get_parse_stats(self) -> Dict[str, Any]:
        """Ottieni statistiche di parsing."""
        stats = self.parse_stats.copy()
        if stats['total_parses'] > 0:
            stats['success_rate'] = stats['successful_parses'] / stats['total_parses']
        else:
            stats['success_rate'] = 0.0
        return stats
    
    def reset_stats(self):
        """Reset statistiche."""
        self.parse_stats = {
            'total_parses': 0,
            'successful_parses': 0,
            'pattern_matches': {},
            'semantic_errors': 0
        }


# Factory function
def create_conceptual_parser(tokenizer: NGConceptualTokenizer) -> NGConceptualParser:
    """Factory per creare parser concettuale."""
    return NGConceptualParser(tokenizer)
