"""
NEUROGLYPH Conceptual Parser - Parser semantico puro.

Implementa parsing diretto da token concettuali a AST semantico
usando pattern matching concettuale invece di grammatiche sintattiche.
"""

from typing import List, Optional, Dict, Any, Callable
from dataclasses import dataclass
from enum import Enum

from ..tokenizer.conceptual_tokenizer import ConceptToken, ConceptType, NGConceptualTokenizer
from ..ast.conceptual_ast import (
    Concept, ConceptualAST, ConceptMetadata, LogicalStructure, ConceptRelation,
    UniversalQuantification, ExistentialQuantification, MaterialImplication,
    LogicalConjunction, LogicalDisjunction, LogicalNegation, Variable, Literal,
    create_variable, create_literal, create_implication, create_conjunction,
    create_disjunction, create_negation, create_universal_quantification,
    create_existential_quantification
)


class ParseError(Exception):
    """Errore di parsing concettuale."""
    pass


class SemanticParseError(ParseError):
    """Errore di parsing semantico - pattern non riconosciuto."""
    pass


@dataclass
class ConceptPattern:
    """
    Pattern concettuale per riconoscimento semantico.
    
    Definisce una sequenza di tipi di token che corrisponde
    a un concetto logico specifico.
    """
    name: str
    token_pattern: List[str]  # Pattern di simboli/tipi
    semantic_builder: Callable[[List[ConceptToken]], Concept]
    precedence: int = 0
    associativity: str = "left"  # "left", "right", "none"
    
    def matches(self, tokens: List[ConceptToken], start_pos: int = 0) -> bool:
        """Verifica se il pattern matcha a partire dalla posizione."""
        if start_pos + len(self.token_pattern) > len(tokens):
            return False
        
        for i, pattern_element in enumerate(self.token_pattern):
            token = tokens[start_pos + i]
            
            if pattern_element.startswith("TYPE:"):
                # Match per tipo semantico
                expected_type = pattern_element[5:]
                if token.semantic_type.value != expected_type:
                    return False
            elif pattern_element.startswith("SYMBOL:"):
                # Match per simbolo specifico
                expected_symbol = pattern_element[7:]
                if token.symbol != expected_symbol:
                    return False
            elif pattern_element == "VARIABLE":
                # Match per variabile
                if token.semantic_type != ConceptType.VARIABLE:
                    return False
            elif pattern_element == "LITERAL":
                # Match per letterale
                if token.semantic_type != ConceptType.LITERAL:
                    return False
            else:
                # Match diretto per simbolo
                if token.symbol != pattern_element:
                    return False
        
        return True
    
    def build_concept(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce concetto dal pattern matchato."""
        return self.semantic_builder(tokens)


class NGConceptualParser:
    """
    Parser semantico puro per NEUROGLYPH.
    
    Usa pattern matching concettuale per riconoscere strutture logiche
    e costruire AST semantico immutabile.
    """
    
    def __init__(self, tokenizer: NGConceptualTokenizer):
        self.tokenizer = tokenizer
        self.patterns = self._initialize_concept_patterns()
        self.parse_stats = {
            'total_parses': 0,
            'successful_parses': 0,
            'pattern_matches': {},
            'semantic_errors': 0
        }
    
    def _initialize_concept_patterns(self) -> List[ConceptPattern]:
        """Inizializza pattern concettuali per riconoscimento semantico."""
        patterns = []
        
        # Pattern quantificazione universale: ∀x: P(x)
        patterns.append(ConceptPattern(
            name="universal_quantification",
            token_pattern=["∀", "VARIABLE", ":", "VARIABLE"],
            semantic_builder=self._build_universal_quantification,
            precedence=10
        ))
        
        # Pattern quantificazione esistenziale: ∃x: P(x)
        patterns.append(ConceptPattern(
            name="existential_quantification", 
            token_pattern=["∃", "VARIABLE", ":", "VARIABLE"],
            semantic_builder=self._build_existential_quantification,
            precedence=10
        ))
        
        # Pattern implicazione: P ⇒ Q
        patterns.append(ConceptPattern(
            name="material_implication",
            token_pattern=["VARIABLE", "⇒", "VARIABLE"],
            semantic_builder=self._build_implication,
            precedence=3
        ))
        
        # Pattern congiunzione: P ∧ Q
        patterns.append(ConceptPattern(
            name="logical_conjunction",
            token_pattern=["VARIABLE", "∧", "VARIABLE"],
            semantic_builder=self._build_conjunction,
            precedence=5
        ))
        
        # Pattern disgiunzione: P ∨ Q
        patterns.append(ConceptPattern(
            name="logical_disjunction",
            token_pattern=["VARIABLE", "∨", "VARIABLE"],
            semantic_builder=self._build_disjunction,
            precedence=4
        ))
        
        # Pattern negazione: ¬P
        patterns.append(ConceptPattern(
            name="logical_negation",
            token_pattern=["¬", "VARIABLE"],
            semantic_builder=self._build_negation,
            precedence=8
        ))
        
        # Pattern variabile singola: P
        patterns.append(ConceptPattern(
            name="variable",
            token_pattern=["VARIABLE"],
            semantic_builder=self._build_variable,
            precedence=1
        ))
        
        # Pattern letterale: 42, "hello"
        patterns.append(ConceptPattern(
            name="literal",
            token_pattern=["LITERAL"],
            semantic_builder=self._build_literal,
            precedence=1
        ))

        # Pattern simboli speciali singoli
        patterns.append(ConceptPattern(
            name="empty_set",
            token_pattern=["∅"],
            semantic_builder=self._build_empty_set,
            precedence=2
        ))

        # Ordina per precedenza (più alta prima)
        patterns.sort(key=lambda p: p.precedence, reverse=True)

        return patterns
    
    def parse(self, neuroglyph_code: str) -> ConceptualAST:
        """
        Parse codice NEUROGLYPH in AST concettuale.
        
        Args:
            neuroglyph_code: Codice simbolico NEUROGLYPH
            
        Returns:
            AST concettuale immutabile
        """
        self.parse_stats['total_parses'] += 1
        
        try:
            # 1. Tokenizza in concetti atomici
            tokens = self.tokenizer.tokenize(neuroglyph_code)
            
            # 2. Filtra whitespace e token non significativi
            filtered_tokens = self._filter_tokens(tokens)
            
            # 3. Parse ricorsivo con pattern matching
            root_concept = self._parse_expression(filtered_tokens)
            
            # 4. Costruisci AST completo
            ast = self._build_conceptual_ast(root_concept, filtered_tokens)
            
            self.parse_stats['successful_parses'] += 1
            return ast
            
        except Exception as e:
            self.parse_stats['semantic_errors'] += 1
            raise SemanticParseError(f"Failed to parse '{neuroglyph_code}': {e}")
    
    def _filter_tokens(self, tokens: List[ConceptToken]) -> List[ConceptToken]:
        """Filtra token non significativi per parsing."""
        filtered = []
        for token in tokens:
            # Salta solo whitespace puro
            if token.symbol.isspace():
                continue
            # Mantieni tutti gli altri token (variabili, letterali, simboli)
            filtered.append(token)
        return filtered
    
    def _parse_expression(self, tokens: List[ConceptToken]) -> Concept:
        """
        Parse ricorsivo di espressione usando pattern matching.
        
        Usa precedenza degli operatori per parsing corretto.
        """
        if not tokens:
            raise SemanticParseError("Empty token sequence")
        
        # Cerca pattern con precedenza più alta
        for pattern in self.patterns:
            for start_pos in range(len(tokens)):
                if pattern.matches(tokens, start_pos):
                    # Pattern trovato - estrai token matchati
                    matched_tokens = tokens[start_pos:start_pos + len(pattern.token_pattern)]
                    
                    # Aggiorna statistiche
                    pattern_name = pattern.name
                    self.parse_stats['pattern_matches'][pattern_name] = (
                        self.parse_stats['pattern_matches'].get(pattern_name, 0) + 1
                    )
                    
                    # Costruisci concetto
                    return pattern.build_concept(matched_tokens)
        
        # Nessun pattern trovato - fallback a variabile singola
        if len(tokens) == 1:
            token = tokens[0]
            if token.semantic_type == ConceptType.VARIABLE:
                return create_variable(token.symbol)
            elif token.semantic_type == ConceptType.LITERAL:
                return self._token_to_literal(token)
        
        raise SemanticParseError(f"No semantic pattern matches token sequence: {[t.symbol for t in tokens]}")
    
    def _build_conceptual_ast(self, root_concept: Concept, tokens: List[ConceptToken]) -> ConceptualAST:
        """Costruisce AST concettuale completo."""
        # Calcola metadati
        metadata = ConceptMetadata(
            complexity_score=self._calculate_complexity(root_concept),
            semantic_depth=self._calculate_depth(root_concept),
            concept_count=len(tokens),
            logical_operators=self._extract_logical_operators(tokens)
        )
        
        # Costruisci struttura logica
        logical_structure = LogicalStructure(
            depth=self._calculate_depth(root_concept),
            operator_precedence=self._extract_operator_precedence(tokens),
            quantifier_scope={}
        )
        
        # Relazioni concettuali (per ora vuote)
        semantic_relations = []
        
        return ConceptualAST(
            root_concept=root_concept,
            semantic_relations=semantic_relations,
            logical_structure=logical_structure,
            concept_metadata=metadata
        )
    
    # Pattern builders
    def _build_universal_quantification(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificazione universale da token."""
        # Pattern: ∀x: P(x)
        variable = tokens[1].symbol
        body_var = tokens[3].symbol
        body = create_variable(body_var)
        return create_universal_quantification(variable, None, body)
    
    def _build_existential_quantification(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce quantificazione esistenziale da token."""
        # Pattern: ∃x: P(x)
        variable = tokens[1].symbol
        body_var = tokens[3].symbol
        body = create_variable(body_var)
        return create_existential_quantification(variable, None, body)
    
    def _build_implication(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce implicazione da token."""
        # Pattern: P ⇒ Q
        premise = create_variable(tokens[0].symbol)
        conclusion = create_variable(tokens[2].symbol)
        return create_implication(premise, conclusion)
    
    def _build_conjunction(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce congiunzione da token."""
        # Pattern: P ∧ Q
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        return create_conjunction(left, right)
    
    def _build_disjunction(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce disgiunzione da token."""
        # Pattern: P ∨ Q
        left = create_variable(tokens[0].symbol)
        right = create_variable(tokens[2].symbol)
        return create_disjunction(left, right)
    
    def _build_negation(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce negazione da token."""
        # Pattern: ¬P
        operand = create_variable(tokens[1].symbol)
        return create_negation(operand)
    
    def _build_variable(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce variabile da token."""
        return create_variable(tokens[0].symbol)
    
    def _build_literal(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce letterale da token."""
        return self._token_to_literal(tokens[0])

    def _build_empty_set(self, tokens: List[ConceptToken]) -> Concept:
        """Costruisce insieme vuoto da token."""
        return create_literal("∅", "special")
    
    def _token_to_literal(self, token: ConceptToken) -> Literal:
        """Converte token in letterale."""
        if "number" in token.concept_name:
            try:
                if '.' in token.symbol:
                    value = float(token.symbol)
                    literal_type = "float"
                else:
                    value = int(token.symbol)
                    literal_type = "int"
            except ValueError:
                value = token.symbol
                literal_type = "string"
        else:
            value = token.symbol
            literal_type = "string"
        
        return create_literal(value, literal_type)
    
    # Utility methods
    def _calculate_complexity(self, concept: Concept) -> float:
        """Calcola complessità concettuale."""
        return concept.metadata.complexity_score
    
    def _calculate_depth(self, concept: Concept) -> int:
        """Calcola profondità semantica."""
        return concept.metadata.semantic_depth
    
    def _extract_logical_operators(self, tokens: List[ConceptToken]) -> List[str]:
        """Estrae operatori logici dai token."""
        operators = []
        for token in tokens:
            if token.concept_name in ['universal_quantification', 'existential_quantification',
                                    'material_implication', 'logical_conjunction', 
                                    'logical_disjunction', 'logical_negation']:
                operators.append(token.symbol)
        return operators
    
    def _extract_operator_precedence(self, tokens: List[ConceptToken]) -> List[str]:
        """Estrae precedenza operatori."""
        # Semplificato per ora
        return [t.symbol for t in tokens if t.semantic_type != ConceptType.VARIABLE]
    
    def get_parse_stats(self) -> Dict[str, Any]:
        """Ottieni statistiche di parsing."""
        stats = self.parse_stats.copy()
        if stats['total_parses'] > 0:
            stats['success_rate'] = stats['successful_parses'] / stats['total_parses']
        else:
            stats['success_rate'] = 0.0
        return stats
    
    def reset_stats(self):
        """Reset statistiche."""
        self.parse_stats = {
            'total_parses': 0,
            'successful_parses': 0,
            'pattern_matches': {},
            'semantic_errors': 0
        }


# Factory function
def create_conceptual_parser(tokenizer: NGConceptualTokenizer) -> NGConceptualParser:
    """Factory per creare parser concettuale."""
    return NGConceptualParser(tokenizer)
