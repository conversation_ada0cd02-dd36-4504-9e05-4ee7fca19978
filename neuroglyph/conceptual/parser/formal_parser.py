#!/usr/bin/env python3
"""
NEUROGLYPH - Formal Parser

Parser formale basato su grammatica EBNF per NEUROGLYPH.
Implementa parsing rigoroso senza fallback permissivi.

PRINCIPI IMMUTABILI:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita (≥95%)
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail
"""

from typing import List, Optional, Union, Dict, Any
from dataclasses import dataclass
from enum import Enum

from ..tokenizer.conceptual_tokenizer import ConceptToken, ConceptType
from ..ast.conceptual_ast import (
    Concept, ConceptualAST, ConceptMetadata, LogicalStructure, ConceptRelation,
    Variable, Literal, FunctionCall,
    UniversalQuantification, ExistentialQuantification,
    LogicalConjunction, LogicalDisjunction, LogicalNegation,
    MaterialImplication, BinaryOperation, GroupedExpression,
    create_variable, create_literal, create_function_call,
    create_universal_quantification, create_existential_quantification,
    create_conjunction, create_disjunction, create_negation,
    create_implication, create_binary_operation, create_grouped_expression
)
from ...core.constants import (
    AUDIT_RESERVED_OPERATORS,
    AUDIT_INVALID_PATTERNS,
    verify_audit_constants_integrity
)


class ParseError(Exception):
    """Errore di parsing formale."""
    pass


class TokenType(Enum):
    """Tipi di token per il parser formale."""
    # Logical operators
    IMPLICATION = "⇒"
    CONJUNCTION = "∧"
    DISJUNCTION = "∨"
    NEGATION = "¬"
    
    # Quantifiers
    UNIVERSAL = "∀"
    EXISTENTIAL = "∃"
    UNIQUE_EXISTENTIAL = "∃!"
    
    # Set operators
    UNION = "∪"
    INTERSECTION = "∩"
    MEMBERSHIP = "∈"
    SUBSET = "⊆"
    SUPERSET = "⊇"
    PROPER_SUBSET = "⊂"
    PROPER_SUPERSET = "⊃"
    EMPTY_SET = "∅"
    
    # Mathematical operators
    PLUS = "+"
    MINUS = "-"
    MULTIPLY = "*"
    DIVIDE = "/"
    EQUALS = "="
    NOT_EQUALS = "≠"
    LESS_THAN = "<"
    GREATER_THAN = ">"
    LESS_EQUAL = "≤"
    GREATER_EQUAL = "≥"
    
    # Calculus operators
    INTEGRAL = "∫"
    SUMMATION = "∑"
    PRODUCT = "∏"
    PARTIAL = "∂"
    
    # Punctuation
    LPAREN = "("
    RPAREN = ")"
    COMMA = ","
    COLON = ":"
    
    # Literals and identifiers
    VARIABLE = "VARIABLE"
    LITERAL = "LITERAL"
    
    # Special
    EOF = "EOF"


@dataclass
class Token:
    """Token per il parser formale."""
    type: TokenType
    value: str
    position: int


class FormalLexer:
    """Lexer formale per NEUROGLYPH."""
    
    def __init__(self, tokens: List[ConceptToken]):
        # Enforce audit lock
        verify_audit_constants_integrity()
        
        self.tokens = tokens
        self.position = 0
        self.formal_tokens: List[Token] = []
        self._tokenize()
    
    def _tokenize(self):
        """Converte ConceptToken in Token formali."""
        for i, concept_token in enumerate(self.tokens):
            symbol = concept_token.symbol
            
            # Map symbols to formal token types
            if symbol == "⇒":
                token_type = TokenType.IMPLICATION
            elif symbol == "∧":
                token_type = TokenType.CONJUNCTION
            elif symbol == "∨":
                token_type = TokenType.DISJUNCTION
            elif symbol == "¬":
                token_type = TokenType.NEGATION
            elif symbol == "∀":
                token_type = TokenType.UNIVERSAL
            elif symbol == "∃":
                token_type = TokenType.EXISTENTIAL
            elif symbol == "∃!":
                token_type = TokenType.UNIQUE_EXISTENTIAL
            elif symbol == "∪":
                token_type = TokenType.UNION
            elif symbol == "∩":
                token_type = TokenType.INTERSECTION
            elif symbol == "∈":
                token_type = TokenType.MEMBERSHIP
            elif symbol == "⊆":
                token_type = TokenType.SUBSET
            elif symbol == "⊇":
                token_type = TokenType.SUPERSET
            elif symbol == "⊂":
                token_type = TokenType.PROPER_SUBSET
            elif symbol == "⊃":
                token_type = TokenType.PROPER_SUPERSET
            elif symbol == "∅":
                token_type = TokenType.EMPTY_SET
            elif symbol == "+":
                token_type = TokenType.PLUS
            elif symbol == "-":
                token_type = TokenType.MINUS
            elif symbol == "*":
                token_type = TokenType.MULTIPLY
            elif symbol == "/":
                token_type = TokenType.DIVIDE
            elif symbol == "=":
                token_type = TokenType.EQUALS
            elif symbol == "≠":
                token_type = TokenType.NOT_EQUALS
            elif symbol == "<":
                token_type = TokenType.LESS_THAN
            elif symbol == ">":
                token_type = TokenType.GREATER_THAN
            elif symbol == "≤":
                token_type = TokenType.LESS_EQUAL
            elif symbol == "≥":
                token_type = TokenType.GREATER_EQUAL
            elif symbol == "∫":
                token_type = TokenType.INTEGRAL
            elif symbol == "∑":
                token_type = TokenType.SUMMATION
            elif symbol == "∏":
                token_type = TokenType.PRODUCT
            elif symbol == "∂":
                token_type = TokenType.PARTIAL
            elif symbol == "(":
                token_type = TokenType.LPAREN
            elif symbol == ")":
                token_type = TokenType.RPAREN
            elif symbol == ",":
                token_type = TokenType.COMMA
            elif symbol == ":":
                token_type = TokenType.COLON
            elif concept_token.semantic_type == ConceptType.VARIABLE:
                if self._is_valid_variable(symbol):
                    token_type = TokenType.VARIABLE
                else:
                    raise ParseError(f"Invalid variable name: '{symbol}' at position {i}")
            elif concept_token.semantic_type == ConceptType.LITERAL:
                if self._is_valid_literal(symbol):
                    token_type = TokenType.LITERAL
                else:
                    raise ParseError(f"Invalid literal: '{symbol}' at position {i}")
            else:
                raise ParseError(f"Unknown symbol: '{symbol}' at position {i}")
            
            self.formal_tokens.append(Token(token_type, symbol, i))
        
        # Add EOF token
        self.formal_tokens.append(Token(TokenType.EOF, "", len(self.tokens)))
    
    def _is_valid_variable(self, symbol: str) -> bool:
        """Verifica rigorosa per variabili."""
        if not symbol or symbol.isspace():
            return False
        
        # Blocca operatori riservati
        if symbol in AUDIT_RESERVED_OPERATORS:
            return False
        
        # Blocca pattern invalidi
        if symbol in AUDIT_INVALID_PATTERNS:
            return False
        
        # Variabili matematiche valide
        if len(symbol) == 1:
            return symbol.isalpha() or symbol in {'ℝ', 'ℕ', 'ℤ', 'ℚ', 'ℂ', 'π', 'e', 'φ', 'θ', 'α', 'β', 'γ'}
        else:
            return symbol.isidentifier()
    
    def _is_valid_literal(self, symbol: str) -> bool:
        """Verifica rigorosa per literal."""
        if not symbol or symbol.isspace():
            return False
        
        # Numeri
        try:
            float(symbol)
            return True
        except ValueError:
            pass
        
        # Stringhe quotate
        if ((symbol.startswith('"') and symbol.endswith('"')) or
            (symbol.startswith("'") and symbol.endswith("'"))):
            return len(symbol) >= 2
        
        # Booleani
        return symbol in {'True', 'False'}


class FormalParser:
    """
    Parser formale ricorsivo discendente per NEUROGLYPH.
    
    Implementa la grammatica EBNF senza fallback permissivi.
    Ogni input deve corrispondere esattamente a una produzione valida.
    """
    
    def __init__(self, tokens: List[ConceptToken]):
        # Enforce audit lock
        verify_audit_constants_integrity()
        
        self.lexer = FormalLexer(tokens)
        self.tokens = self.lexer.formal_tokens
        self.position = 0
        self.current_token = self.tokens[0] if self.tokens else None
    
    def parse(self) -> ConceptualAST:
        """
        Parse principale - punto di ingresso.
        
        Returns:
            ConceptualAST: AST parsato
            
        Raises:
            ParseError: Se l'input non corrisponde alla grammatica
        """
        if not self.tokens or len(self.tokens) == 1:  # Solo EOF
            raise ParseError("Empty input - no valid NEUROGLYPH expression")
        
        try:
            root_concept = self._parse_neuroglyph_expression()
            
            # Verifica che tutti i token siano stati consumati
            if self.current_token.type != TokenType.EOF:
                raise ParseError(f"Unexpected token after complete expression: '{self.current_token.value}'")

            # Crea ConceptualAST con tutti i parametri richiesti
            metadata = ConceptMetadata(
                complexity_score=0.5,
                semantic_depth=1
            )

            logical_structure = LogicalStructure(
                depth=1,
                operator_precedence=[],
                quantifier_scope={}
            )

            return ConceptualAST(
                root_concept=root_concept,
                semantic_relations=[],
                logical_structure=logical_structure,
                concept_metadata=metadata
            )
            
        except ParseError:
            raise
        except Exception as e:
            raise ParseError(f"Parsing failed: {e}")
    
    def _advance(self):
        """Avanza al token successivo."""
        if self.position < len(self.tokens) - 1:
            self.position += 1
            self.current_token = self.tokens[self.position]
    
    def _peek(self, offset: int = 1) -> Optional[Token]:
        """Guarda avanti senza consumare token."""
        peek_pos = self.position + offset
        if peek_pos < len(self.tokens):
            return self.tokens[peek_pos]
        return None
    
    def _expect(self, token_type: TokenType) -> Token:
        """Consuma token del tipo atteso o solleva errore."""
        if self.current_token.type != token_type:
            raise ParseError(
                f"Expected {token_type.value}, got '{self.current_token.value}' "
                f"at position {self.current_token.position}"
            )
        token = self.current_token
        self._advance()
        return token
    
    def _parse_neuroglyph_expression(self) -> Concept:
        """Parse dell'espressione NEUROGLYPH principale."""
        # Prova ogni tipo di espressione in ordine di precedenza
        
        # Quantified expressions (highest precedence)
        if self.current_token.type in {TokenType.UNIVERSAL, TokenType.EXISTENTIAL, TokenType.UNIQUE_EXISTENTIAL}:
            return self._parse_quantified_expression()
        
        # Logical expressions
        return self._parse_logical_expression()
    
    def _parse_quantified_expression(self) -> Concept:
        """Parse espressioni quantificate."""
        if self.current_token.type == TokenType.UNIVERSAL:
            return self._parse_universal_quantification()
        elif self.current_token.type == TokenType.EXISTENTIAL:
            return self._parse_existential_quantification()
        elif self.current_token.type == TokenType.UNIQUE_EXISTENTIAL:
            return self._parse_unique_existential_quantification()
        else:
            raise ParseError(f"Expected quantifier, got '{self.current_token.value}'")
    
    def _parse_universal_quantification(self) -> Concept:
        """Parse quantificazione universale: ∀x: P(x) o ∀x ∃y: R(x,y)"""
        self._expect(TokenType.UNIVERSAL)
        
        # Variable
        var_token = self._expect(TokenType.VARIABLE)
        variable = var_token.value
        
        # Optional domain
        domain = None
        if self.current_token.type == TokenType.MEMBERSHIP:
            self._advance()  # consume ∈
            domain_token = self._expect(TokenType.VARIABLE)
            domain = create_variable(domain_token.value)
        
        # Colon or space for nested quantifier
        if self.current_token.type == TokenType.COLON:
            self._advance()  # consume :
            body = self._parse_logical_expression()
        elif self.current_token.type == TokenType.EXISTENTIAL:
            # Nested quantifier: ∀x ∃y: R(x,y)
            body = self._parse_existential_quantification()
        else:
            raise ParseError(f"Expected ':' or nested quantifier after universal quantifier variable")
        
        return create_universal_quantification(variable, domain, body)
    
    def _parse_existential_quantification(self) -> Concept:
        """Parse quantificazione esistenziale: ∃x: P(x)"""
        self._expect(TokenType.EXISTENTIAL)
        
        # Variable
        var_token = self._expect(TokenType.VARIABLE)
        variable = var_token.value
        
        # Optional domain
        domain = None
        if self.current_token.type == TokenType.MEMBERSHIP:
            self._advance()  # consume ∈
            domain_token = self._expect(TokenType.VARIABLE)
            domain = create_variable(domain_token.value)
        
        # Colon
        self._expect(TokenType.COLON)
        
        # Body
        body = self._parse_logical_expression()
        
        return create_existential_quantification(variable, domain, body)
    
    def _parse_unique_existential_quantification(self) -> Concept:
        """Parse quantificazione esistenziale unica: ∃!x: P(x)"""
        self._expect(TokenType.UNIQUE_EXISTENTIAL)
        
        # Variable
        var_token = self._expect(TokenType.VARIABLE)
        variable = var_token.value
        
        # Optional domain
        domain = None
        if self.current_token.type == TokenType.MEMBERSHIP:
            self._advance()  # consume ∈
            domain_token = self._expect(TokenType.VARIABLE)
            domain = create_variable(domain_token.value)
        
        # Colon
        self._expect(TokenType.COLON)
        
        # Body
        body = self._parse_logical_expression()
        
        # Note: Unique existential is represented as regular existential for now
        # Could be extended with a specific AST node type
        return create_existential_quantification(variable, domain, body)
    
    def _parse_logical_expression(self) -> Concept:
        """Parse espressioni logiche con precedenza."""
        return self._parse_implication()
    
    def _parse_implication(self) -> Concept:
        """Parse implicazione (precedenza più bassa)."""
        left = self._parse_disjunction()
        
        if self.current_token.type == TokenType.IMPLICATION:
            self._advance()  # consume ⇒
            right = self._parse_implication()  # Right-associative
            return create_implication(left, right)
        
        return left
    
    def _parse_disjunction(self) -> Concept:
        """Parse disgiunzione."""
        left = self._parse_conjunction()
        
        while self.current_token.type == TokenType.DISJUNCTION:
            self._advance()  # consume ∨
            right = self._parse_conjunction()
            left = create_disjunction(left, right)
        
        return left
    
    def _parse_conjunction(self) -> Concept:
        """Parse congiunzione."""
        left = self._parse_negation()
        
        while self.current_token.type == TokenType.CONJUNCTION:
            self._advance()  # consume ∧
            right = self._parse_negation()
            left = create_conjunction(left, right)
        
        return left
    
    def _parse_negation(self) -> Concept:
        """Parse negazione."""
        if self.current_token.type == TokenType.NEGATION:
            self._advance()  # consume ¬
            operand = self._parse_primary()
            return create_negation(operand)
        
        return self._parse_primary()
    
    def _parse_primary(self) -> Concept:
        """Parse espressioni primarie."""
        if self.current_token.type == TokenType.LPAREN:
            return self._parse_grouped_expression()
        elif self.current_token.type == TokenType.VARIABLE:
            return self._parse_variable_or_function()
        elif self.current_token.type == TokenType.LITERAL:
            return self._parse_literal()
        else:
            raise ParseError(f"Unexpected token in primary expression: '{self.current_token.value}'")
    
    def _parse_grouped_expression(self) -> Concept:
        """Parse espressione raggruppata: (expr)"""
        self._expect(TokenType.LPAREN)
        expr = self._parse_logical_expression()
        self._expect(TokenType.RPAREN)
        return create_grouped_expression(expr)
    
    def _parse_variable_or_function(self) -> Concept:
        """Parse variabile o chiamata di funzione."""
        var_token = self._expect(TokenType.VARIABLE)
        
        # Check for function call
        if self.current_token.type == TokenType.LPAREN:
            return self._parse_function_call(var_token.value)
        else:
            return create_variable(var_token.value)
    
    def _parse_function_call(self, function_name: str) -> Concept:
        """Parse chiamata di funzione: f(x) o f(x,y)"""
        self._expect(TokenType.LPAREN)
        
        # Parse arguments
        args = []
        if self.current_token.type != TokenType.RPAREN:
            args.append(self._parse_argument())
            
            while self.current_token.type == TokenType.COMMA:
                self._advance()  # consume ,
                args.append(self._parse_argument())
        
        self._expect(TokenType.RPAREN)
        
        return create_function_call(function_name, args)
    
    def _parse_argument(self) -> Concept:
        """Parse argomento di funzione."""
        if self.current_token.type == TokenType.VARIABLE:
            var_token = self._expect(TokenType.VARIABLE)
            return create_variable(var_token.value)
        elif self.current_token.type == TokenType.LITERAL:
            return self._parse_literal()
        else:
            raise ParseError(f"Expected variable or literal in function argument, got '{self.current_token.value}'")
    
    def _parse_literal(self) -> Concept:
        """Parse literal."""
        lit_token = self._expect(TokenType.LITERAL)
        
        # Determine literal type and value
        value = lit_token.value
        
        # Try number
        try:
            if '.' in value:
                return create_literal(float(value), "float")
            else:
                return create_literal(int(value), "int")
        except ValueError:
            pass
        
        # Try boolean
        if value in {'True', 'False'}:
            return create_literal(value == 'True', "bool")
        
        # String
        if ((value.startswith('"') and value.endswith('"')) or
            (value.startswith("'") and value.endswith("'"))):
            return create_literal(value[1:-1], "string")
        
        raise ParseError(f"Invalid literal format: '{value}'")


def create_formal_parser(tokens: List[ConceptToken]) -> FormalParser:
    """Factory per creare parser formale."""
    return FormalParser(tokens)
