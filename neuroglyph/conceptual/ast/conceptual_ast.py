"""
NEUROGLYPH Conceptual AST - AST semantico puro per linguaggio concettuale.

Definisce nodi AST immutabili che rappresentano concetti logici universali,
indipendenti da qualunque linguaggio di programmazione specifico.
"""

from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Union
from abc import ABC, abstractmethod
from enum import Enum


class ConceptualNodeType(Enum):
    """Tipi di nodi AST concettuali."""
    QUANTIFIED = "quantified"
    LOGICAL = "logical"
    MATHEMATICAL = "mathematical"
    SET_THEORY = "set_theory"
    INFERENCE = "inference"
    META = "meta"
    FUNCTION_APPLICATION = "function_application"
    FUNCTIONAL = "functional"
    OPERATIONAL = "operational"
    VARIABLE = "variable"
    LITERAL = "literal"


@dataclass(frozen=True)
class ConceptualAST:
    """
    AST semantico puro - immutabile e universale.
    
    Rappresenta la struttura concettuale completa di un'espressione NEUROGLYPH.
    """
    root_concept: 'Concept'
    semantic_relations: List['ConceptRelation']
    logical_structure: 'LogicalStructure'
    concept_metadata: 'ConceptMetadata'
    
    def semantically_equivalent(self, other: 'ConceptualAST') -> bool:
        """Verifica equivalenza semantica con altro AST."""
        return (self.root_concept.semantically_equivalent(other.root_concept) and
                self.logical_structure.equivalent(other.logical_structure))
    
    def to_neuroglyph(self) -> str:
        """Serializza AST in codice NEUROGLYPH."""
        return self.root_concept.to_neuroglyph()
    
    def logical_strength(self) -> float:
        """Calcola forza logica complessiva dell'AST."""
        return self.root_concept.logical_strength()


@dataclass(frozen=True)
class ConceptMetadata:
    """Metadati concettuali."""
    complexity_score: float = 0.0
    semantic_depth: int = 1
    concept_count: int = 1
    logical_operators: List[str] = None
    
    def __post_init__(self):
        if self.logical_operators is None:
            object.__setattr__(self, 'logical_operators', [])


@dataclass(frozen=True)
class LogicalStructure:
    """Struttura logica dell'espressione."""
    depth: int
    operator_precedence: List[str]
    quantifier_scope: Dict[str, int]
    
    def equivalent(self, other: 'LogicalStructure') -> bool:
        """Verifica equivalenza strutturale."""
        return (self.depth == other.depth and
                self.operator_precedence == other.operator_precedence)


@dataclass(frozen=True)
class ConceptRelation:
    """Relazione tra concetti."""
    relation_type: str
    source_concept: 'Concept'
    target_concept: 'Concept'
    strength: float = 1.0


class Concept(ABC):
    """
    Concetto atomico base - classe astratta immutabile.
    
    Ogni concetto rappresenta un'unità semantica indivisibile
    con significato logico preciso e forza intrinseca.
    """
    
    def __init__(self):
        self._concept_id: int = 0
        self._node_type: ConceptualNodeType = ConceptualNodeType.VARIABLE
        self._metadata: ConceptMetadata = ConceptMetadata()
    
    @property
    def concept_id(self) -> int:
        return self._concept_id
    
    @property
    def node_type(self) -> ConceptualNodeType:
        return self._node_type
    
    @property
    def metadata(self) -> ConceptMetadata:
        return self._metadata
    
    @abstractmethod
    def semantic_meaning(self) -> str:
        """Restituisce significato semantico del concetto."""
        pass
    
    @abstractmethod
    def logical_strength(self) -> float:
        """Restituisce forza logica del concetto (0.0-1.0)."""
        pass
    
    @abstractmethod
    def to_neuroglyph(self) -> str:
        """Serializza concetto in notazione NEUROGLYPH."""
        pass
    
    @abstractmethod
    def semantically_equivalent(self, other: 'Concept') -> bool:
        """Verifica equivalenza semantica con altro concetto."""
        pass
    
    def __str__(self) -> str:
        return self.to_neuroglyph()
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}({self.to_neuroglyph()})"


@dataclass(frozen=True)
class UniversalQuantification(Concept):
    """
    Quantificazione universale: ∀x ∈ A: P(x)
    
    Rappresenta il concetto logico "per tutti gli elementi del dominio".
    """
    variable: str
    domain: Optional['Concept'] = None
    body: Optional['Concept'] = None
    
    def __post_init__(self):
        object.__setattr__(self, '_concept_id', 1001)
        object.__setattr__(self, '_node_type', ConceptualNodeType.QUANTIFIED)
        object.__setattr__(self, '_metadata', ConceptMetadata(
            complexity_score=0.9,
            semantic_depth=2,
            logical_operators=['∀']
        ))
    
    def semantic_meaning(self) -> str:
        domain_str = f" in {self.domain.semantic_meaning()}" if self.domain else ""
        body_str = self.body.semantic_meaning() if self.body else "true"
        return f"For all {self.variable}{domain_str}: {body_str}"
    
    def logical_strength(self) -> float:
        return 1.0  # Quantificazione universale ha forza massima
    
    def to_neuroglyph(self) -> str:
        """Serializza quantificazione universale in NEUROGLYPH con gestione errori robusta."""
        try:
            domain_part = ""
            if self.domain:
                if hasattr(self.domain, 'to_neuroglyph'):
                    domain_part = f" ∈ {self.domain.to_neuroglyph()}"
                else:
                    domain_part = f" ∈ {self.domain}"

            body_part = ""
            if self.body:
                if hasattr(self.body, 'to_neuroglyph'):
                    body_part = f": {self.body.to_neuroglyph()}"
                else:
                    body_part = f": {self.body}"

            return f"∀{self.variable}{domain_part}{body_part}"
        except Exception:
            # Fallback sicuro
            return f"∀{self.variable}: {self.body if self.body else 'P'}"
    
    def semantically_equivalent(self, other: Concept) -> bool:
        if not isinstance(other, UniversalQuantification):
            return False
        return (self.variable == other.variable and
                (self.domain is None and other.domain is None or
                 self.domain and other.domain and self.domain.semantically_equivalent(other.domain)) and
                (self.body is None and other.body is None or
                 self.body and other.body and self.body.semantically_equivalent(other.body)))


@dataclass(frozen=True)
class ExistentialQuantification(Concept):
    """
    Quantificazione esistenziale: ∃x ∈ A: P(x)
    
    Rappresenta il concetto logico "esiste almeno un elemento del dominio".
    """
    variable: str
    domain: Optional['Concept'] = None
    body: Optional['Concept'] = None
    
    def __post_init__(self):
        object.__setattr__(self, '_concept_id', 1002)
        object.__setattr__(self, '_node_type', ConceptualNodeType.QUANTIFIED)
        object.__setattr__(self, '_metadata', ConceptMetadata(
            complexity_score=0.8,
            semantic_depth=2,
            logical_operators=['∃']
        ))
    
    def semantic_meaning(self) -> str:
        domain_str = f" in {self.domain.semantic_meaning()}" if self.domain else ""
        body_str = self.body.semantic_meaning() if self.body else "true"
        return f"There exists {self.variable}{domain_str}: {body_str}"
    
    def logical_strength(self) -> float:
        return 0.7  # Esistenziale ha forza minore dell'universale
    
    def to_neuroglyph(self) -> str:
        """Serializza quantificazione esistenziale in NEUROGLYPH con gestione errori robusta."""
        try:
            domain_part = ""
            if self.domain:
                if hasattr(self.domain, 'to_neuroglyph'):
                    domain_part = f" ∈ {self.domain.to_neuroglyph()}"
                else:
                    domain_part = f" ∈ {self.domain}"

            body_part = ""
            if self.body:
                if hasattr(self.body, 'to_neuroglyph'):
                    body_part = f": {self.body.to_neuroglyph()}"
                else:
                    body_part = f": {self.body}"

            return f"∃{self.variable}{domain_part}{body_part}"
        except Exception:
            # Fallback sicuro
            return f"∃{self.variable}: {self.body if self.body else 'P'}"
    
    def semantically_equivalent(self, other: Concept) -> bool:
        if not isinstance(other, ExistentialQuantification):
            return False
        return (self.variable == other.variable and
                (self.domain is None and other.domain is None or
                 self.domain and other.domain and self.domain.semantically_equivalent(other.domain)) and
                (self.body is None and other.body is None or
                 self.body and other.body and self.body.semantically_equivalent(other.body)))


@dataclass(frozen=True)
class MaterialImplication(Concept):
    """
    Implicazione materiale: P ⇒ Q
    
    Rappresenta il concetto logico "se premessa allora conclusione".
    """
    premise: 'Concept'
    conclusion: 'Concept'
    
    def __post_init__(self):
        object.__setattr__(self, '_concept_id', 1101)
        object.__setattr__(self, '_node_type', ConceptualNodeType.LOGICAL)
        object.__setattr__(self, '_metadata', ConceptMetadata(
            complexity_score=0.6,
            semantic_depth=1,
            logical_operators=['⇒']
        ))
    
    def semantic_meaning(self) -> str:
        return f"If {self.premise.semantic_meaning()} then {self.conclusion.semantic_meaning()}"
    
    def logical_strength(self) -> float:
        return 0.8  # Implicazione ha forza alta ma non massima
    
    def to_neuroglyph(self) -> str:
        return f"{self.premise.to_neuroglyph()} ⇒ {self.conclusion.to_neuroglyph()}"
    
    def semantically_equivalent(self, other: Concept) -> bool:
        if not isinstance(other, MaterialImplication):
            return False
        return (self.premise.semantically_equivalent(other.premise) and
                self.conclusion.semantically_equivalent(other.conclusion))


@dataclass(frozen=True)
class LogicalConjunction(Concept):
    """
    Congiunzione logica: P ∧ Q
    
    Rappresenta il concetto logico "entrambi veri".
    """
    left: 'Concept'
    right: 'Concept'
    
    def __post_init__(self):
        object.__setattr__(self, '_concept_id', 1103)
        object.__setattr__(self, '_node_type', ConceptualNodeType.LOGICAL)
        object.__setattr__(self, '_metadata', ConceptMetadata(
            complexity_score=0.4,
            semantic_depth=1,
            logical_operators=['∧']
        ))
    
    def semantic_meaning(self) -> str:
        return f"{self.left.semantic_meaning()} and {self.right.semantic_meaning()}"
    
    def logical_strength(self) -> float:
        return 1.0  # Congiunzione ha forza massima
    
    def to_neuroglyph(self) -> str:
        return f"{self.left.to_neuroglyph()} ∧ {self.right.to_neuroglyph()}"
    
    def semantically_equivalent(self, other: Concept) -> bool:
        if not isinstance(other, LogicalConjunction):
            return False
        # Congiunzione è commutativa
        return ((self.left.semantically_equivalent(other.left) and
                 self.right.semantically_equivalent(other.right)) or
                (self.left.semantically_equivalent(other.right) and
                 self.right.semantically_equivalent(other.left)))


@dataclass(frozen=True)
class LogicalDisjunction(Concept):
    """
    Disgiunzione logica: P ∨ Q
    
    Rappresenta il concetto logico "almeno uno vero".
    """
    left: 'Concept'
    right: 'Concept'
    
    def __post_init__(self):
        object.__setattr__(self, '_concept_id', 1104)
        object.__setattr__(self, '_node_type', ConceptualNodeType.LOGICAL)
        object.__setattr__(self, '_metadata', ConceptMetadata(
            complexity_score=0.4,
            semantic_depth=1,
            logical_operators=['∨']
        ))
    
    def semantic_meaning(self) -> str:
        return f"{self.left.semantic_meaning()} or {self.right.semantic_meaning()}"
    
    def logical_strength(self) -> float:
        return 0.6  # Disgiunzione ha forza media
    
    def to_neuroglyph(self) -> str:
        return f"{self.left.to_neuroglyph()} ∨ {self.right.to_neuroglyph()}"
    
    def semantically_equivalent(self, other: Concept) -> bool:
        if not isinstance(other, LogicalDisjunction):
            return False
        # Disgiunzione è commutativa
        return ((self.left.semantically_equivalent(other.left) and
                 self.right.semantically_equivalent(other.right)) or
                (self.left.semantically_equivalent(other.right) and
                 self.right.semantically_equivalent(other.left)))


@dataclass(frozen=True)
class LogicalNegation(Concept):
    """
    Negazione logica: ¬P
    
    Rappresenta il concetto logico "non vero".
    """
    operand: 'Concept'
    
    def __post_init__(self):
        object.__setattr__(self, '_concept_id', 1105)
        object.__setattr__(self, '_node_type', ConceptualNodeType.LOGICAL)
        object.__setattr__(self, '_metadata', ConceptMetadata(
            complexity_score=0.3,
            semantic_depth=1,
            logical_operators=['¬']
        ))
    
    def semantic_meaning(self) -> str:
        return f"not {self.operand.semantic_meaning()}"
    
    def logical_strength(self) -> float:
        return 1.0  # Negazione ha forza massima
    
    def to_neuroglyph(self) -> str:
        return f"¬{self.operand.to_neuroglyph()}"
    
    def semantically_equivalent(self, other: Concept) -> bool:
        if not isinstance(other, LogicalNegation):
            return False
        return self.operand.semantically_equivalent(other.operand)


@dataclass(frozen=True)
class Variable(Concept):
    """
    Variabile: x, y, variable_name
    
    Rappresenta un identificatore simbolico.
    """
    name: str
    
    def __post_init__(self):
        object.__setattr__(self, '_concept_id', 0)  # ID 0 per variabili
        object.__setattr__(self, '_node_type', ConceptualNodeType.VARIABLE)
        object.__setattr__(self, '_metadata', ConceptMetadata(
            complexity_score=0.1,
            semantic_depth=0
        ))
    
    def semantic_meaning(self) -> str:
        return f"variable '{self.name}'"
    
    def logical_strength(self) -> float:
        return 0.5  # Variabili hanno forza neutra
    
    def to_neuroglyph(self) -> str:
        """Serializza variabile in NEUROGLYPH."""
        # Gestisce nomi complessi come "integral_f_x"
        if '_' in self.name and any(prefix in self.name for prefix in ['integral', 'sum', 'product', 'partial']):
            # Ricostruisci forma matematica
            if self.name.startswith('integral_'):
                parts = self.name.split('_')
                if len(parts) >= 3:
                    return f"∫ {parts[1]}({parts[2]}) d{parts[2]}"
            elif self.name.startswith('sum_'):
                var = self.name.replace('sum_', '')
                return f"∑ {var}"
            elif self.name.startswith('product_'):
                var = self.name.replace('product_', '')
                return f"∏ {var}"
            elif self.name.startswith('partial_'):
                parts = self.name.split('_')
                if len(parts) >= 3:
                    return f"∂{parts[1]}/∂{parts[2]}"

        return self.name
    
    def semantically_equivalent(self, other: Concept) -> bool:
        if not isinstance(other, Variable):
            return False
        return self.name == other.name


@dataclass(frozen=True)
class FunctionCall(Concept):
    """
    Chiamata di funzione: P(x), f(x, y), R(a, b, c)

    Rappresenta l'applicazione di una funzione o predicato.
    """
    function_name: str
    arguments: List['Concept']

    def __post_init__(self):
        object.__setattr__(self, '_concept_id', 1200)
        object.__setattr__(self, '_node_type', ConceptualNodeType.FUNCTIONAL)
        object.__setattr__(self, '_metadata', ConceptMetadata(
            complexity_score=0.5,
            semantic_depth=1
        ))

    def semantic_meaning(self) -> str:
        args_str = ", ".join(arg.semantic_meaning() for arg in self.arguments)
        return f"function {self.function_name} applied to ({args_str})"

    def logical_strength(self) -> float:
        return 0.8  # Chiamate di funzione hanno forza alta

    def to_neuroglyph(self) -> str:
        """Serializza chiamata di funzione in NEUROGLYPH."""
        if not self.arguments:
            return self.function_name

        args_str = ", ".join(arg.to_neuroglyph() for arg in self.arguments)
        return f"{self.function_name}({args_str})"

    def semantically_equivalent(self, other: Concept) -> bool:
        if not isinstance(other, FunctionCall):
            return False
        return (self.function_name == other.function_name and
                len(self.arguments) == len(other.arguments) and
                all(a.semantically_equivalent(b) for a, b in zip(self.arguments, other.arguments)))


@dataclass(frozen=True)
class BinaryOperation(Concept):
    """
    Operazione binaria: x + y, A ∪ B, x ∈ A

    Rappresenta operazioni con due operandi.
    """
    left: 'Concept'
    operator: str
    right: 'Concept'

    def __post_init__(self):
        object.__setattr__(self, '_concept_id', 1300)
        object.__setattr__(self, '_node_type', ConceptualNodeType.OPERATIONAL)
        object.__setattr__(self, '_metadata', ConceptMetadata(
            complexity_score=0.6,
            semantic_depth=1,
            logical_operators=[self.operator]
        ))

    def semantic_meaning(self) -> str:
        return f"{self.left.semantic_meaning()} {self.operator} {self.right.semantic_meaning()}"

    def logical_strength(self) -> float:
        return 0.7  # Operazioni binarie hanno forza medio-alta

    def to_neuroglyph(self) -> str:
        """Serializza operazione binaria in NEUROGLYPH."""
        left_str = self.left.to_neuroglyph()
        right_str = self.right.to_neuroglyph()

        # Gestisci precedenza con parentesi se necessario
        if hasattr(self.left, 'operator') or hasattr(self.right, 'operator'):
            return f"({left_str} {self.operator} {right_str})"
        else:
            return f"{left_str} {self.operator} {right_str}"

    def semantically_equivalent(self, other: Concept) -> bool:
        if not isinstance(other, BinaryOperation):
            return False

        # Verifica operatori commutativi
        commutative_ops = {'∧', '∨', '∪', '∩', '+', '×', '⊗', '⊕'}

        if self.operator == other.operator:
            if self.operator in commutative_ops:
                # Operatore commutativo: A op B ≡ B op A
                return ((self.left.semantically_equivalent(other.left) and
                         self.right.semantically_equivalent(other.right)) or
                        (self.left.semantically_equivalent(other.right) and
                         self.right.semantically_equivalent(other.left)))
            else:
                # Operatore non commutativo
                return (self.left.semantically_equivalent(other.left) and
                        self.right.semantically_equivalent(other.right))

        return False


@dataclass(frozen=True)
class Literal(Concept):
    """
    Letterale: 42, 3.14, "hello"
    
    Rappresenta un valore costante.
    """
    value: Union[int, float, str, bool]
    literal_type: str
    
    def __post_init__(self):
        object.__setattr__(self, '_concept_id', 0)  # ID 0 per letterali
        object.__setattr__(self, '_node_type', ConceptualNodeType.LITERAL)
        object.__setattr__(self, '_metadata', ConceptMetadata(
            complexity_score=0.0,
            semantic_depth=0
        ))
    
    def semantic_meaning(self) -> str:
        return f"{self.literal_type} literal '{self.value}'"
    
    def logical_strength(self) -> float:
        return 1.0  # Letterali hanno significato preciso
    
    def to_neuroglyph(self) -> str:
        if self.literal_type == "string":
            return f'"{self.value}"'
        return str(self.value)
    
    def semantically_equivalent(self, other: Concept) -> bool:
        if not isinstance(other, Literal):
            return False
        return self.value == other.value and self.literal_type == other.literal_type


# Factory functions per creazione concetti
def create_variable(name: str) -> Variable:
    """Factory per creare variabili."""
    return Variable(name)


def create_literal(value: Union[int, float, str], literal_type: str = None) -> Literal:
    """Factory per creare letterali."""
    if literal_type is None:
        if isinstance(value, int):
            literal_type = "int"
        elif isinstance(value, float):
            literal_type = "float"
        elif isinstance(value, str):
            literal_type = "string"
        else:
            literal_type = "unknown"
    
    return Literal(value, literal_type)


def create_implication(premise: Concept, conclusion: Concept) -> MaterialImplication:
    """Factory per creare implicazioni."""
    return MaterialImplication(premise, conclusion)


def create_conjunction(left: Concept, right: Concept) -> LogicalConjunction:
    """Factory per creare congiunzioni."""
    return LogicalConjunction(left, right)


def create_disjunction(left: Concept, right: Concept) -> LogicalDisjunction:
    """Factory per creare disgiunzioni."""
    return LogicalDisjunction(left, right)


def create_negation(operand: Concept) -> LogicalNegation:
    """Factory per creare negazioni."""
    return LogicalNegation(operand)


def create_universal_quantification(variable: str, domain: Concept = None, body: Concept = None) -> UniversalQuantification:
    """Factory per creare quantificazioni universali."""
    return UniversalQuantification(variable, domain, body)


def create_existential_quantification(variable: str, domain: Concept = None, body: Concept = None) -> ExistentialQuantification:
    """Factory per creare quantificazioni esistenziali."""
    return ExistentialQuantification(variable, domain, body)


def create_function_call(function_name: str, arguments: List[Concept] = None) -> FunctionCall:
    """Factory per creare chiamate di funzione."""
    if arguments is None:
        arguments = []
    return FunctionCall(function_name, arguments)


def create_binary_operation(left: Concept, operator: str, right: Concept) -> BinaryOperation:
    """Factory per creare operazioni binarie."""
    return BinaryOperation(left, operator, right)
