{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# 🧪 NEUROGLYPH - Test Dipendenze\n", "\n", "## 🎯 **OBIETTIVO**\n", "Testare e risolvere problemi di compatibilità tra torch, transformers, Unsloth\n", "\n", "## 🔧 **ISTRUZIONI**\n", "1. <PERSON><PERSON><PERSON><PERSON> le celle in ordine\n", "2. <PERSON><PERSON> restart runtime quando richiesto\n", "3. Verifica che tutti i test passino\n", "\n", "---"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_unsloth"}, "outputs": [], "source": ["# 🚀 STEP 1: Installa Unsloth (gestisce torch/transformers automaticamente)\n", "print(\"🔧 Installando Unsloth...\")\n", "!pip install \"unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git\"\n", "\n", "print(\"\\n⚠️ IMPORTANTE: Ora vai su Runtime → Restart Runtime\")\n", "print(\"🔄 Poi esegui la cella successiva\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "verify_base"}, "outputs": [], "source": ["# 🔍 STEP 2: Verifica installazione base\n", "print(\"🔍 Verificando installazione base...\")\n", "\n", "try:\n", "    import torch\n", "    print(f\"✅ PyTorch: {torch.__version__}\")\n", "    print(f\"✅ CUDA: {torch.cuda.is_available()}\")\n", "    if torch.cuda.is_available():\n", "        print(f\"✅ GPU: {torch.cuda.get_device_name(0)}\")\n", "except Exception as e:\n", "    print(f\"❌ PyTorch error: {e}\")\n", "\n", "try:\n", "    import transformers\n", "    print(f\"✅ Transformers: {transformers.__version__}\")\n", "except Exception as e:\n", "    print(f\"❌ Transformers error: {e}\")\n", "\n", "try:\n", "    from unsloth import FastLanguageModel, is_bfloat16_supported\n", "    print(f\"✅ Unsloth: importato con successo\")\n", "    print(f\"✅ BFloat16: {is_bfloat16_supported()}\")\n", "except Exception as e:\n", "    print(f\"❌ Unsloth error: {e}\")\n", "    print(\"🔄 Prova a riavviare runtime e rieseguire\")\n", "\n", "print(\"\\n🎯 Se tutto è ✅, procedi con la cella successiva\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_compatible"}, "outputs": [], "source": ["# 🔧 STEP 3: Installa dipendenze compatibili\n", "print(\"🔧 Installando dipendenze compatibili...\")\n", "\n", "# Installa senza sovrascrivere torch/transformers\n", "!pip install --no-deps datasets==2.12.0\n", "!pip install --no-deps peft==0.6.0\n", "!pip install --no-deps trl==0.7.10\n", "!pip install --no-deps bitsandbytes==0.41.1\n", "!pip install lark-parser==0.12.0\n", "\n", "print(\"✅ Dipendenze installate\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "verify_all"}, "outputs": [], "source": ["# 🧪 STEP 4: Verifica completa\n", "print(\"🧪 VERIFICA COMPLETA DIPENDENZE\")\n", "print(\"=\"*50)\n", "\n", "all_good = True\n", "\n", "# Test PyTorch + CUDA\n", "try:\n", "    import torch\n", "    print(f\"✅ PyTorch: {torch.__version__}\")\n", "    print(f\"✅ CUDA: {torch.cuda.is_available()}\")\n", "    if torch.cuda.is_available():\n", "        print(f\"✅ GPU: {torch.cuda.get_device_name(0)}\")\n", "        print(f\"✅ Memory: {torch.cuda.get_device_properties(0).total_memory // 1024**3} GB\")\n", "except Exception as e:\n", "    print(f\"❌ PyTorch: {e}\")\n", "    all_good = False\n", "\n", "# Test Transformers\n", "try:\n", "    import transformers\n", "    print(f\"✅ Transformers: {transformers.__version__}\")\n", "except Exception as e:\n", "    print(f\"❌ Transformers: {e}\")\n", "    all_good = False\n", "\n", "# Test Unsloth\n", "try:\n", "    from unsloth import FastLanguageModel, is_bfloat16_supported\n", "    print(f\"✅ Unsloth: FastLanguageModel OK\")\n", "    print(f\"✅ BFloat16: {is_bfloat16_supported()}\")\n", "except Exception as e:\n", "    print(f\"❌ Unsloth: {e}\")\n", "    all_good = False\n", "\n", "# Test ML Libraries\n", "try:\n", "    import datasets, peft, trl, bitsandbytes\n", "    print(f\"✅ Datasets: {datasets.__version__}\")\n", "    print(f\"✅ PEFT: {peft.__version__}\")\n", "    print(f\"✅ TRL: {trl.__version__}\")\n", "    print(f\"✅ BitsAndBytes: {bitsandbytes.__version__}\")\n", "except Exception as e:\n", "    print(f\"❌ ML Libraries: {e}\")\n", "    all_good = False\n", "\n", "# Test NEUROGLYPH specific\n", "try:\n", "    from lark import Lark\n", "    print(f\"✅ Lark: parser OK\")\n", "except Exception as e:\n", "    print(f\"❌ Lark: {e}\")\n", "    all_good = False\n", "\n", "print(\"\\n\" + \"=\"*50)\n", "if all_good:\n", "    print(\"🎉 TUTTE LE DIPENDENZE FUNZIONANO!\")\n", "    print(\"🚀 Pronto per NEUROGLYPH NG GODMODE v1\")\n", "else:\n", "    print(\"❌ Ci sono ancora problemi\")\n", "    print(\"🔧 Consulta INSTALLATION_GUIDE.md\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "test_model_loading"}, "outputs": [], "source": ["# 🤖 STEP 5: Test caricamento modello (opzionale)\n", "print(\"🤖 Test caricamento modello...\")\n", "\n", "try:\n", "    from unsloth import FastLanguageModel\n", "    \n", "    # Test con modello piccolo\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name=\"unsloth/llama-2-7b-bnb-4bit\",\n", "        max_seq_length=512,\n", "        dtype=None,\n", "        load_in_4bit=True,\n", "    )\n", "    \n", "    print(f\"✅ Modello caricato: {model.config.model_type}\")\n", "    print(f\"✅ Tokenizer: {len(tokenizer)} tokens\")\n", "    \n", "    # Cleanup\n", "    del model, tokenizer\n", "    import gc\n", "    gc.collect()\n", "    torch.cuda.empty_cache()\n", "    \n", "    print(\"✅ Test modello: SUCCESSO\")\n", "    \n", "except Exception as e:\n", "    print(f\"⚠️ Test modello fallito: {e}\")\n", "    print(\"📝 Normale se hai poca memoria GPU\")\n", "    print(\"🎯 Le dipendenze funzionano comunque\")\n", "\n", "print(\"\\n🏆 SETUP COMPLETATO!\")\n", "print(\"🚀 Ora puoi usare il notebook principale NG_GODMODE_v1_Fine_Tuning.ipynb\")"]}]}