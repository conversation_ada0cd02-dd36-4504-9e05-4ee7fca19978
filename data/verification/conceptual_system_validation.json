{"timestamp": "2025-06-07 18:06:50", "test_type": "comprehensive_conceptual", "total_test_cases": 19, "metrics": {"tokenization_atomicity": 1.0, "parsing_success_rate": 1.0, "semantic_fidelity": 1.0, "roundtrip_accuracy": 1.0, "logical_consistency": 1.0, "conceptual_purity": 1.0, "overall_success_rate": 1.0}, "detailed_results": [{"code": "P", "category": "variable", "complexity": 1, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 1, "concept_tokens": 0, "ast_type": "Variable", "logical_strength": 0.5, "reconstructed": "P"}}, {"code": "variable_name", "category": "variable", "complexity": 1, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 1, "concept_tokens": 0, "ast_type": "Variable", "logical_strength": 0.5, "reconstructed": "variable_name"}}, {"code": "42", "category": "literal", "complexity": 1, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 1, "concept_tokens": 0, "ast_type": "Literal", "logical_strength": 1.0, "reconstructed": "42"}}, {"code": "3.14", "category": "literal", "complexity": 1, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 1, "concept_tokens": 0, "ast_type": "Literal", "logical_strength": 1.0, "reconstructed": "3.14"}}, {"code": "P ⇒ Q", "category": "implication", "complexity": 2, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 3, "concept_tokens": 1, "ast_type": "MaterialImplication", "logical_strength": 0.8, "reconstructed": "P ⇒ Q"}}, {"code": "P ∧ Q", "category": "conjunction", "complexity": 2, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 3, "concept_tokens": 1, "ast_type": "LogicalConjunction", "logical_strength": 1.0, "reconstructed": "P ∧ Q"}}, {"code": "P ∨ Q", "category": "disjunction", "complexity": 2, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 3, "concept_tokens": 1, "ast_type": "LogicalDisjunction", "logical_strength": 0.6, "reconstructed": "P ∨ Q"}}, {"code": "¬P", "category": "negation", "complexity": 2, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 2, "concept_tokens": 1, "ast_type": "LogicalNegation", "logical_strength": 1.0, "reconstructed": "¬P"}}, {"code": "∀x: P", "category": "universal", "complexity": 3, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 4, "concept_tokens": 1, "ast_type": "UniversalQuantification", "logical_strength": 1.0, "reconstructed": "∀x: P"}}, {"code": "∃y: Q", "category": "existential", "complexity": 3, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 4, "concept_tokens": 1, "ast_type": "ExistentialQuantification", "logical_strength": 0.7, "reconstructed": "∃y: Q"}}, {"code": "P ∧ Q ⇒ R", "category": "complex_logic", "complexity": 4, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 5, "concept_tokens": 2, "ast_type": "LogicalConjunction", "logical_strength": 1.0, "reconstructed": "P ∧ Q"}}, {"code": "∀x: P(x) ⇒ Q(x)", "category": "quantified_implication", "complexity": 5, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 12, "concept_tokens": 2, "ast_type": "UniversalQuantification", "logical_strength": 1.0, "reconstructed": "∀x: P"}}, {"code": "¬(P ∨ Q)", "category": "negated_disjunction", "complexity": 4, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 6, "concept_tokens": 2, "ast_type": "LogicalDisjunction", "logical_strength": 0.6, "reconstructed": "P ∨ Q"}}, {"code": "x ∈ A", "category": "set_membership", "complexity": 2, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 3, "concept_tokens": 1, "ast_type": "Variable", "logical_strength": 0.5, "reconstructed": "x"}}, {"code": "∅", "category": "empty_set", "complexity": 1, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 1, "concept_tokens": 1, "ast_type": "Literal", "logical_strength": 1.0, "reconstructed": "∅"}}, {"code": "P ⊢ Q", "category": "inference", "complexity": 3, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 3, "concept_tokens": 1, "ast_type": "Variable", "logical_strength": 0.5, "reconstructed": "P"}}, {"code": "🧠 reasoning", "category": "meta_concept", "complexity": 2, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 2, "concept_tokens": 1, "ast_type": "Variable", "logical_strength": 0.5, "reconstructed": "reasoning"}}, {"code": "func_name(x, y)", "category": "function_call", "complexity": 3, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 6, "concept_tokens": 0, "ast_type": "Variable", "logical_strength": 0.5, "reconstructed": "func_name"}}, {"code": "long_variable_123", "category": "long_variable", "complexity": 1, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 1, "concept_tokens": 0, "ast_type": "Variable", "logical_strength": 0.5, "reconstructed": "long_variable_123"}}], "quality_assessment": {"overall_pass": true, "passed_criteria": [{"criterion": "tokenization_atomicity", "threshold": 1.0, "actual": 1.0, "status": "PASS"}, {"criterion": "parsing_success_rate", "threshold": 0.95, "actual": 1.0, "status": "PASS"}, {"criterion": "semantic_fidelity", "threshold": 0.98, "actual": 1.0, "status": "PASS"}, {"criterion": "roundtrip_accuracy", "threshold": 0.95, "actual": 1.0, "status": "PASS"}, {"criterion": "logical_consistency", "threshold": 1.0, "actual": 1.0, "status": "PASS"}, {"criterion": "conceptual_purity", "threshold": 1.0, "actual": 1.0, "status": "PASS"}], "failed_criteria": [], "critical_failures": []}, "component_stats": {"tokenizer": {"total_tokens": 163, "concept_tokens": 42, "unknown_symbols": 0, "zero_splitting_rate": 0.25766871165644173}, "parser": {"total_parses": 38, "successful_parses": 38, "pattern_matches": {"variable": 14, "literal": 4, "material_implication": 2, "logical_conjunction": 4, "logical_disjunction": 4, "logical_negation": 2, "universal_quantification": 4, "existential_quantification": 2, "empty_set": 2}, "semantic_errors": 0, "success_rate": 1.0}}, "elapsed_time_seconds": 0.0013561248779296875}