{"timestamp": "2025-06-07 18:04:17", "test_type": "comprehensive_conceptual", "total_test_cases": 19, "metrics": {"tokenization_atomicity": 1.0, "parsing_success_rate": 0.8421052631578947, "semantic_fidelity": 0.8421052631578947, "roundtrip_accuracy": 0.8421052631578947, "logical_consistency": 0.8421052631578947, "conceptual_purity": 0.8421052631578947, "overall_success_rate": 0.8421052631578947}, "detailed_results": [{"code": "P", "category": "variable", "complexity": 1, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 1, "concept_tokens": 0, "ast_type": "Variable", "logical_strength": 0.5, "reconstructed": "P"}}, {"code": "variable_name", "category": "variable", "complexity": 1, "success": false, "tokenization_success": true, "parsing_success": false, "roundtrip_success": false, "semantic_fidelity": 0.0, "logical_consistency": false, "conceptual_purity": false, "error": "Semantic parse error: Failed to parse 'variable_name': Empty token sequence", "details": {"token_count": 0, "concept_tokens": 0}}, {"code": "42", "category": "literal", "complexity": 1, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 1, "concept_tokens": 0, "ast_type": "Literal", "logical_strength": 1.0, "reconstructed": "42"}}, {"code": "3.14", "category": "literal", "complexity": 1, "success": false, "tokenization_success": true, "parsing_success": false, "roundtrip_success": false, "semantic_fidelity": 0.0, "logical_consistency": false, "conceptual_purity": false, "error": "Semantic parse error: Failed to parse '3.14': Empty token sequence", "details": {"token_count": 0, "concept_tokens": 0}}, {"code": "P ⇒ Q", "category": "implication", "complexity": 2, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 3, "concept_tokens": 1, "ast_type": "MaterialImplication", "logical_strength": 0.8, "reconstructed": "P ⇒ Q"}}, {"code": "P ∧ Q", "category": "conjunction", "complexity": 2, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 3, "concept_tokens": 1, "ast_type": "LogicalConjunction", "logical_strength": 1.0, "reconstructed": "P ∧ Q"}}, {"code": "P ∨ Q", "category": "disjunction", "complexity": 2, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 3, "concept_tokens": 1, "ast_type": "LogicalDisjunction", "logical_strength": 0.6, "reconstructed": "P ∨ Q"}}, {"code": "¬P", "category": "negation", "complexity": 2, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 2, "concept_tokens": 1, "ast_type": "LogicalNegation", "logical_strength": 1.0, "reconstructed": "¬P"}}, {"code": "∀x: P", "category": "universal", "complexity": 3, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 4, "concept_tokens": 1, "ast_type": "UniversalQuantification", "logical_strength": 1.0, "reconstructed": "∀x: P"}}, {"code": "∃y: Q", "category": "existential", "complexity": 3, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 4, "concept_tokens": 1, "ast_type": "ExistentialQuantification", "logical_strength": 0.7, "reconstructed": "∃y: Q"}}, {"code": "P ∧ Q ⇒ R", "category": "complex_logic", "complexity": 4, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 5, "concept_tokens": 2, "ast_type": "LogicalConjunction", "logical_strength": 1.0, "reconstructed": "P ∧ Q"}}, {"code": "∀x: P(x) ⇒ Q(x)", "category": "quantified_implication", "complexity": 5, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 12, "concept_tokens": 2, "ast_type": "UniversalQuantification", "logical_strength": 1.0, "reconstructed": "∀x: P"}}, {"code": "¬(P ∨ Q)", "category": "negated_disjunction", "complexity": 4, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 6, "concept_tokens": 2, "ast_type": "LogicalDisjunction", "logical_strength": 0.6, "reconstructed": "P ∨ Q"}}, {"code": "x ∈ A", "category": "set_membership", "complexity": 2, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 3, "concept_tokens": 1, "ast_type": "Variable", "logical_strength": 0.5, "reconstructed": "x"}}, {"code": "∅", "category": "empty_set", "complexity": 1, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 1, "concept_tokens": 1, "ast_type": "Literal", "logical_strength": 1.0, "reconstructed": "∅"}}, {"code": "P ⊢ Q", "category": "inference", "complexity": 3, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 3, "concept_tokens": 1, "ast_type": "Variable", "logical_strength": 0.5, "reconstructed": "P"}}, {"code": "🧠 reasoning", "category": "meta_concept", "complexity": 2, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 2, "concept_tokens": 1, "ast_type": "Variable", "logical_strength": 0.5, "reconstructed": "reasoning"}}, {"code": "func_name(x, y)", "category": "function_call", "complexity": 3, "success": true, "tokenization_success": true, "parsing_success": true, "roundtrip_success": true, "semantic_fidelity": 1.0, "logical_consistency": true, "conceptual_purity": true, "error": null, "details": {"token_count": 5, "concept_tokens": 0, "ast_type": "Variable", "logical_strength": 0.5, "reconstructed": "x"}}, {"code": "long_variable_123", "category": "long_variable", "complexity": 1, "success": false, "tokenization_success": true, "parsing_success": false, "roundtrip_success": false, "semantic_fidelity": 0.0, "logical_consistency": false, "conceptual_purity": false, "error": "Semantic parse error: Failed to parse 'long_variable_123': Empty token sequence", "details": {"token_count": 0, "concept_tokens": 0}}], "quality_assessment": {"overall_pass": false, "passed_criteria": [{"criterion": "tokenization_atomicity", "threshold": 1.0, "actual": 1.0, "status": "PASS"}], "failed_criteria": [{"criterion": "parsing_success_rate", "threshold": 0.95, "actual": 0.8421052631578947, "status": "FAIL"}, {"criterion": "semantic_fidelity", "threshold": 0.98, "actual": 0.8421052631578947, "status": "FAIL"}, {"criterion": "roundtrip_accuracy", "threshold": 0.95, "actual": 0.8421052631578947, "status": "FAIL"}, {"criterion": "logical_consistency", "threshold": 1.0, "actual": 0.8421052631578947, "status": "FAIL"}, {"criterion": "conceptual_purity", "threshold": 1.0, "actual": 0.8421052631578947, "status": "FAIL"}], "critical_failures": ["conceptual_purity"]}, "component_stats": {"tokenizer": {"total_tokens": 160, "concept_tokens": 42, "unknown_symbols": 8, "zero_splitting_rate": 0.2625}, "parser": {"total_parses": 35, "successful_parses": 32, "pattern_matches": {"variable": 10, "literal": 2, "material_implication": 2, "logical_conjunction": 4, "logical_disjunction": 4, "logical_negation": 2, "universal_quantification": 4, "existential_quantification": 2, "empty_set": 2}, "semantic_errors": 3, "success_rate": 0.9142857142857143}}, "elapsed_time_seconds": 0.0013849735260009766}